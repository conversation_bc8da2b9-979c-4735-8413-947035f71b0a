"""
Unit tests for combined_bundles.py
"""
import os
import shutil
import tempfile
import pytest
from click.testing import <PERSON><PERSON><PERSON><PERSON><PERSON>
from dice_elipy_scripts.combined_bundles import cli

def create_dummy_bundles(path, files):
    os.makedirs(path, exist_ok=True)
    for f in files:
        with open(os.path.join(path, f), "w") as fp:
            fp.write("dummy")

def test_combined_bundles_head_only(monkeypatch):
    temp_main = tempfile.mkdtemp()
    temp_combine = tempfile.mkdtemp()
    temp_output = tempfile.mkdtemp()
    temp_network = tempfile.mkdtemp()
    # Patch out elipy2 and avalanche/filer logic
    monkeypatch.setattr("dice_elipy_scripts.combined_bundles.local_paths.get_local_bundles_path", lambda **kwargs: temp_main)
    monkeypatch.setattr("dice_elipy_scripts.combined_bundles.filer.FilerUtils.fetch_head_bundles", lambda *a, **k: create_dummy_bundles(temp_main, ["a.bundle"]))
    monkeypatch.setattr("dice_elipy_scripts.combined_bundles.filer.FilerUtils.deploy_avalanche_combine_output", lambda *a, **k: None)
    monkeypatch.setattr("dice_elipy_scripts.combined_bundles.avalanche.combine", lambda *a, **k: create_dummy_bundles(temp_output, ["combined.bundle"]))
    runner = CliRunner()
    result = runner.invoke(cli, [
        "win64", "final",
        "--code-branch", "main", "--code-changelist", "123",
        "--data-branch", "main", "--data-changelist", "456",
        "--combine-code-branch", "main2", "--combine-code-changelist", "789",
        "--combine-data-branch", "main2", "--combine-data-changelist", "1011",
        "--network-share-path", temp_network
    ])
    assert result.exit_code == 0
    assert os.path.exists(os.path.join(temp_network, "combined.bundle"))
    shutil.rmtree(temp_main)
    shutil.rmtree(temp_combine)
    shutil.rmtree(temp_output)
    shutil.rmtree(temp_network)

def test_combined_bundles_with_delta(monkeypatch):
    temp_main = tempfile.mkdtemp()
    temp_combine = tempfile.mkdtemp()
    temp_output = tempfile.mkdtemp()
    temp_network = tempfile.mkdtemp()
    temp_baseline = tempfile.mkdtemp()
    temp_delta = tempfile.mkdtemp()
    monkeypatch.setattr("dice_elipy_scripts.combined_bundles.local_paths.get_local_bundles_path", lambda **kwargs: temp_main)
    monkeypatch.setattr("dice_elipy_scripts.combined_bundles.filer.FilerUtils.fetch_head_bundles", lambda *a, **k: create_dummy_bundles(temp_main, ["a.bundle"]))
    monkeypatch.setattr("dice_elipy_scripts.combined_bundles.filer.FilerUtils.deploy_avalanche_combine_output", lambda *a, **k: None)
    monkeypatch.setattr("dice_elipy_scripts.combined_bundles.filer.FilerUtils.fetch_baseline_bundles", lambda *a, **k: create_dummy_bundles(temp_baseline, ["baseline.bundle"]))
    monkeypatch.setattr("dice_elipy_scripts.combined_bundles.avalanche.combine", lambda *a, **k: create_dummy_bundles(temp_output, ["combined.bundle"]))
    monkeypatch.setattr("dice_elipy_scripts.combined_bundles.avalanche.ddelta", lambda *a, **k: create_dummy_bundles(temp_delta, ["delta.bundle"]))
    monkeypatch.setattr("dice_elipy_scripts.combined_bundles.filer.FilerUtils.deploy_delta_bundles", lambda *a, **k: None)
    runner = CliRunner()
    result = runner.invoke(cli, [
        "win64", "final",
        "--code-branch", "main", "--code-changelist", "123",
        "--data-branch", "main", "--data-changelist", "456",
        "--combine-code-branch", "main2", "--combine-code-changelist", "789",
        "--combine-data-branch", "main2", "--combine-data-changelist", "1011",
        "--produce-delta-bundles",
        "--baseline-code-branch", "base", "--baseline-code-changelist", "222",
        "--baseline-data-branch", "base", "--baseline-data-changelist", "333",
        "--network-share-path", temp_network
    ])
    assert result.exit_code == 0
    assert os.path.exists(os.path.join(temp_network, "combined.bundle"))
    shutil.rmtree(temp_main)
    shutil.rmtree(temp_combine)
    shutil.rmtree(temp_output)
    shutil.rmtree(temp_network)
    shutil.rmtree(temp_baseline)
    shutil.rmtree(temp_delta)

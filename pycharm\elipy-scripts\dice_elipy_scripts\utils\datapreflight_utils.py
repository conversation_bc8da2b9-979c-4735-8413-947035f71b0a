"""
datapreflight_utils.py

Utility module for datapreflights.
"""
import json
import os
import re
from dataclasses import dataclass
from itertools import islice
from typing import List, Union
from elipy2 import frostbite_core, LOGGER
from elipy2.exceptions import ELIPYException


def get_dbx_assets_to_cook(
    p4_shelved_files_info: List,
    datadir_name: str = "data",
    content_layer: str = "",
    exclude_files: bool = True,
) -> List[str]:
    """
    Get a list of assets that can be cooked individually.
    When checking for asset types to skip, we need to check for both 'ModuleName.TypeName'
    and 'TypeName' if we have 'ModuleName.TypeName' in the asset file.

    Note:
    - Does not support source data modules (i.e. dbx files under TnT/Code/...).
      That would need parsing of "win64-dll/release/{licensee_name}Pipeline.datamodules" TODO

    :param p4_shelved_files_info: List of files that we should filter.
    :param datadir_name: Relative to GAME_ROOT, eg: 'bfdata', 'kindata', 'dicenextdata'
    :param content_layer: Active content layer name. See: contentLayers.json
    :param exclude_files: Set if we are excluding some types of dbx files.
    :return: Returns a list of files to cook individually.
    """
    if content_layer.lower() == "source":
        content_layer = ""
    individual_dbx_assets = []
    skipped_asset_types = get_skipped_asset_types()
    # If we wanted to match all layers that might have been edited
    # We could use the following, however that would require the pipeline
    # to somehow be invoked with all the layers.
    # Attempting to cook an asset which ONLY exists on a higher level (than Source)
    # with the default settings would fail.
    # Maybe failing on that is "good"?
    # It would help catch edits to layers which are not being cooked?
    layer_names_expression = "[0-9a-z_]+"
    asset_depot_regex_str = "{0}/(?P<layer>(?:Source|Layers/(?:{1})))/(?P<asset>.*)\\.dbx$".format(
        datadir_name, layer_names_expression
    )
    all_layers_pattern = re.compile(asset_depot_regex_str, re.IGNORECASE)
    asset_depot_regex_str = f"{datadir_name}/(?P<layer>(?:Source))/(?P<asset>.*)\\.dbx$"
    # Otherwise match only the 'active' layers hierarchy
    if content_layer:
        layer_hierarchy_names = get_layer_hierarchy(content_layer)
        layer_names_expression = "|".join(layer_hierarchy_names)
        asset_depot_regex_str = (
            "{0}/(?P<layer>(?:Source|Layers/(?:{1})))/(?P<asset>.*)\\.dbx$".format(
                datadir_name, layer_names_expression
            )
        )

    pattern = re.compile(asset_depot_regex_str, re.IGNORECASE)
    for item in p4_shelved_files_info:
        if b"action" in item and b"depotFile" in item:
            line = item[b"depotFile"].decode("utf-8")
            action = item[b"action"].decode("utf-8")
            # Exclude unwanted files from the cook
            is_delete_action = "delete" in action
            match_exclusions = None
            if exclude_files:
                match_exclusions = re.search(
                    "Shaders/|Animations/|MaterialGrid/Part_", line, re.IGNORECASE
                )
            if not (match_exclusions or is_delete_action):
                asset_match = pattern.search(line)
                if asset_match:
                    asset_name = asset_match.group("asset")
                    layer_sub_path = asset_match.group("layer")
                    primary_instance_type = get_primary_instance_type(asset_name, layer_sub_path)
                    skipped_asset_type = (
                        primary_instance_type in skipped_asset_types
                        or primary_instance_type.split(".")[1] in skipped_asset_types
                    )
                    if not skipped_asset_type:
                        individual_dbx_assets.append(asset_name)
                else:
                    asset_match = all_layers_pattern.search(line)
                    if asset_match:
                        asset_name = asset_match.group("asset")
                        layer_sub_path = asset_match.group("layer")
                        LOGGER.warning(
                            "Asset '{0}' in layer '{1}' has been changed but it will not be cooked"
                            " as it is not in the active content layer '{2}'".format(
                                asset_name, layer_sub_path, content_layer
                            )
                        )

    LOGGER.info("Individual dbx assets: {}".format(individual_dbx_assets))
    return individual_dbx_assets


def extract_layers_from_file_list(
    p4_unshelved_files: List,
    datadir: str = "data",
) -> List[str]:
    """
    Extract a list of content layers that can be cooked using p4 unshelved files list.

    :param p4_unshelved_files: List of unshelved files.
    :param datadir: Relative to GAME_ROOT, eg: 'bfdata', 'kindata', 'dicenextdata'
    :return: Returns a list of layers to cook individually.
    """
    content_layers = []
    for item in p4_unshelved_files:  # pylint: disable=too-many-nested-blocks
        if b"action" in item and b"depotFile" in item:
            depot_path = item[b"depotFile"].decode("utf-8")
            # action = item[b"action"].decode("utf-8")
            layer_names_expression = "[0-9a-z_]+"
            asset_depot_regex_str = (
                "{0}/(?P<layer>(?:Source|Layers/(?:{1})))/(?P<asset>.*)\\.dbx$".format(
                    datadir, layer_names_expression
                )
            )
            all_layers_pattern = re.compile(asset_depot_regex_str, re.IGNORECASE)
            asset_match = all_layers_pattern.search(depot_path)
            if asset_match:
                layer_name = asset_match.group("layer")
                # Regex matches Layers/<layer_name>. Removing Layers/ prefix
                if layer_name.startswith("Layers/"):
                    layer_name = layer_name.replace("Layers/", "")
                if layer_name not in content_layers:
                    asset_name = asset_match.group("asset")
                    LOGGER.info("Adding {} because {} was changed".format(depot_path, asset_name))
                    content_layers.append(layer_name)
                    # Add parents in the hierarchy too
                    if layer_name.lower() != "source":
                        layer_hierarchy_names = get_layer_hierarchy(layer_name)
                        for layer in layer_hierarchy_names:
                            if layer not in content_layers:
                                content_layers.append(layer)

    return content_layers


@dataclass
class ContentLayer:
    """
    Class to set up parent/child hierarchy nodes of content layers
    from the configuration contentLayers.json
    """

    name: str
    parent_name: str
    display_name: str
    parent: Union[object, None] = None


def get_layer_hierarchy(
    content_layer: str, layer_settings_path: Union[None, str] = None
) -> List[str]:
    """
    Given the active content layer name returns the list of content layers
    (folders) that comprise of it by traversing the parent hierarchy
    """
    layer_hierarchy_names = []
    if layer_settings_path is None:
        layer_settings_path = os.path.join(
            frostbite_core.get_game_data_dir(), "Config", "AssetHub", "contentLayers.json"
        )
    if not os.path.isfile(layer_settings_path):
        raise ELIPYException("contentLayer.json was not found.")
    with open(layer_settings_path, "r") as settings_file:
        layer_settings = json.loads(settings_file.read())
        name_to_layer = {
            layer_data["name"]: ContentLayer(
                name=layer_data["name"],
                parent_name=layer_data["parent"],
                display_name=layer_data["displayName"],
            )
            for layer_data in layer_settings["layers"]
        }

        for layer in name_to_layer.values():
            layer.parent = name_to_layer.get(layer.parent_name)

        if not content_layer in name_to_layer:
            raise ELIPYException(
                f"Layer named '{content_layer}' was not found in contentLayers.json."
            )

        in_hierarchy = name_to_layer[content_layer]
        while in_hierarchy:
            layer_hierarchy_names.append(in_hierarchy.name)
            in_hierarchy = in_hierarchy.parent

    return layer_hierarchy_names


def get_skipped_asset_types(skip_cook_type_file: Union[None, str] = None) -> List[str]:
    """
    Get a list of asset types which will be skipped.
    The types may be of two formats: either 'TypeName' or 'ModuleName.TypeName'.

    :param skip_cook_type_file: Path to the file where asset types to skip are listed.
    :return: Returns a list of asset types to skip.
    """
    if skip_cook_type_file is None:
        skip_cook_type_file = os.path.join(
            frostbite_core.get_game_data_dir(), "Config", "DRE", "SkipCookTypeList.json"
        )
    if not os.path.exists(skip_cook_type_file):
        LOGGER.info("No list of asset types to skip found.")
        return []
    with open(skip_cook_type_file, "r") as json_file:
        file_data = json.loads(json_file.read())
        skipped_asset_types = file_data["SkipAssetTypes"]
        LOGGER.info("Skipped asset types: %s", skipped_asset_types)
        return skipped_asset_types


def get_primary_instance_type(
    asset_name: str,
    asset_layer_path: str = "Source",
    asset_base_path: str = frostbite_core.get_game_data_dir(),
) -> str:
    """
    Get the primary instance type for an asset file.
    This type is found on the third line in a correctly formatted asset file.
    The types are of the format 'ModuleName.TypeName'.

    Source Data Module asset paths are not supported.

    :param asset_name: The virtual path for the asset in a frostbite database (i.e. without .dbx).
    :param asset_layer_path: The path realtive to <datadir>. Default "Source"
        May have values like "Layers/SomeContentLayerName/"
    :param asset_base_path: The base path for the asset. Default is the <datadir> folder.
    :return: Returns the primary instance type for an asset file.
    """
    asset_file_path = os.path.join(asset_base_path, asset_layer_path, asset_name + ".dbx")
    with open(asset_file_path, encoding="utf8") as asset_file:
        for line in list(islice(asset_file, 2, 3)):
            pattern = re.compile('type="([A-Za-z0-9._]+)"')
            filtered_result = pattern.findall(line)
            if filtered_result:
                return filtered_result[0]
            else:
                raise ELIPYException(
                    "No primary instance type found in {} "
                    "(either the type is missing, or the file format has been changed)".format(
                        asset_name
                    )
                )

#!/usr/bin/env python3
"""
Test script to verify the retention=0 fix works correctly
"""

def test_retention_slicing():
    """Test the Python slicing behavior that was causing the bug"""
    
    print("Testing Python slicing behavior for retention=0 fix")
    print("=" * 60)
    
    # Simulate a list of builds (sorted by age, oldest first)
    disk_builds_sorted = ["build1", "build2", "build3", "build4", "build5"]
    
    print(f"Test builds: {disk_builds_sorted}")
    print()
    
    # Test different retention values
    for maxamounti in [0, 1, 2, 3, 5, 10]:
        print(f"Testing maxamounti = {maxamounti}")
        
        # OLD BUGGY LOGIC
        old_reserve_list = disk_builds_sorted[-maxamounti:]
        old_expire_list = disk_builds_sorted[:-maxamounti]
        
        # NEW FIXED LOGIC
        if maxamounti == 0:
            new_reserve_list = []
            new_expire_list = disk_builds_sorted[:]
        else:
            new_reserve_list = disk_builds_sorted[-maxamounti:]
            new_expire_list = disk_builds_sorted[:-maxamounti]
        
        print(f"  OLD: reserve={old_reserve_list}, expire={old_expire_list}")
        print(f"  NEW: reserve={new_reserve_list}, expire={new_expire_list}")
        
        # Check if the fix addresses the bug
        if maxamounti == 0:
            if len(old_expire_list) == 0 and len(new_expire_list) == len(disk_builds_sorted):
                print(f"  ✅ BUG FIXED: retention=0 now deletes all builds")
            else:
                print(f"  ❌ BUG NOT FIXED")
        else:
            if old_expire_list == new_expire_list and old_reserve_list == new_reserve_list:
                print(f"  ✅ NORMAL CASE: no change for retention > 0")
            else:
                print(f"  ❌ REGRESSION: normal case broken")
        
        print()

def test_apply_retention_policies_logic():
    """Test the actual _apply_retention_policies logic"""
    
    print("Testing _apply_retention_policies logic")
    print("=" * 60)
    
    # Mock builds
    disk_builds_sorted = [f"build_{i:03d}" for i in range(1, 151)]  # 150 builds
    
    print(f"Total builds: {len(disk_builds_sorted)}")
    
    # Test retention = 0 (should delete all)
    maxamounti = 0
    release_candidates_to_keep_count = 0
    
    print(f"Testing retention = {maxamounti}")
    
    # Apply the fixed logic
    if maxamounti == 0:
        reserve_list = []
        expire_list = disk_builds_sorted[:]
    else:
        reserve_list = disk_builds_sorted[-maxamounti:]
        expire_list = disk_builds_sorted[:-maxamounti]
    
    print(f"Reserve list length: {len(reserve_list)}")
    print(f"Expire list length: {len(expire_list)}")
    
    if len(expire_list) == 150 and len(reserve_list) == 0:
        print("✅ SUCCESS: retention=0 correctly marks all 150 builds for deletion")
    else:
        print("❌ FAILURE: retention=0 logic is still broken")

if __name__ == "__main__":
    test_retention_slicing()
    print()
    test_apply_retention_policies_logic()

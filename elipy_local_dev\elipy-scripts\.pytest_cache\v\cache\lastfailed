{"dice_elipy_scripts/tests/test_combined_bundles.py::test_combined_bundles_head_only": true, "dice_elipy_scripts/tests/test_combined_bundles.py::test_combined_bundles_with_delta": true, "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds": true, "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_use_onefs_api": true, "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_includes": true, "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_excludes": true, "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_no_bilbo": true}
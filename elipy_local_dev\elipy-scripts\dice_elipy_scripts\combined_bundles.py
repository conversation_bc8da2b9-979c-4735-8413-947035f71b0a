"""
combined_bundles.py

Script to produce combined bundles (head and optionally delta) for a given platform.
This script is intended to be called as a separate build job, replacing the redundant
combine logic in frosty and patchfrosty jobs.

Usage:
    elipy --location <location> combined_bundles <platform> <config> [options]

Options:
    --code-branch, --code-changelist, --data-branch, --data-changelist
    --combine-code-branch, --combine-code-changelist, --combine-data-branch, --combine-data-changelist
    --combine-settings-file, --produce-delta-bundles, --baseline-branches/changelists
    --network-share-path, --dry-run, etc.

See frosty.py and patch_frosty.py for detailed option descriptions.
"""

import click
import os
from dice_elipy_scripts.utils.frosty_build_utils import (
    patch_eainstaller_signtool,
)
from elipy2 import (
    avalanche,
    filer,
    local_paths,
    LOGGER,
    SETTINGS,
)
from elipy2.exceptions import ELIPYException


@click.command(
    "combined_bundles", short_help="Produce combined bundles (head and delta) for a platform."
)
@click.argument("platform")
@click.argument("config")
@click.option("--code-branch", required=True)
@click.option("--code-changelist", required=True)
@click.option("--data-branch", required=True)
@click.option("--data-changelist", required=True)
@click.option("--combine-code-branch", required=True)
@click.option("--combine-code-changelist", required=True)
@click.option("--combine-data-branch", required=True)
@click.option("--combine-data-changelist", required=True)
@click.option("--combine-settings-file", default=None)
@click.option(
    "--produce-delta-bundles", is_flag=True, help="Produce delta bundles after combining."
)
@click.option("--baseline-code-branch", default=None)
@click.option("--baseline-code-changelist", default=None)
@click.option("--baseline-data-branch", default=None)
@click.option("--baseline-data-changelist", default=None)
@click.option("--network-share-path", required=True, help="Path to copy output bundles.")
@click.option("--dry-run", is_flag=True)
def cli(
    platform,
    config,
    code_branch,
    code_changelist,
    data_branch,
    data_changelist,
    combine_code_branch,
    combine_code_changelist,
    combine_data_branch,
    combine_data_changelist,
    combine_settings_file,
    produce_delta_bundles,
    baseline_code_branch,
    baseline_code_changelist,
    baseline_data_branch,
    baseline_data_changelist,
    network_share_path,
    dry_run,
):
    """
    Produce combined bundles (head and optionally delta) for a platform and copy to network share.
    """
    # Prepare output locations
    bundles_location_main = os.path.split(
        local_paths.get_local_bundles_path(deployed_bundles_dir_name="deployed_bundles_main")
    )[0]
    bundles_location_combine = os.path.split(
        local_paths.get_local_bundles_path(deployed_bundles_dir_name="deployed_bundles_combine")
    )[0]
    output_dir = os.path.split(
        local_paths.get_local_bundles_path(deployed_bundles_dir_name="combine_bundles")
    )[0]

    # Fetch head bundles set 1
    _filer = filer.FilerUtils()
    _filer.fetch_head_bundles(
        code_branch=code_branch,
        code_changelist=code_changelist,
        data_branch=data_branch,
        data_changelist=data_changelist,
        platform=platform,
        dest=bundles_location_main,
        bundles_dir_name="combine_bundles",
    )
    # Fetch head bundles set 2
    _filer.fetch_head_bundles(
        code_branch=combine_code_branch,
        code_changelist=combine_code_changelist,
        data_branch=combine_data_branch,
        data_changelist=combine_data_changelist,
        platform=platform,
        dest=bundles_location_combine,
        bundles_dir_name="combine_bundles",
    )
    # Combine the two sets of bundles
    extra_combine_args = ["-s"]
    if combine_settings_file:
        extra_combine_args.append(combine_settings_file)
    else:
        if platform == "xbsx":
            extra_combine_args.append("project-combine-hres-smart-delivery.yaml")
        else:
            extra_combine_args.append("project-combine-hres.yaml")
    avalanche.combine(
        input_dir_1=bundles_location_main,
        input_dir_2=bundles_location_combine,
        output_dir=output_dir,
        extra_combine_args=extra_combine_args,
    )
    if not dry_run:
        # Deploy combined bundles for safekeeping
        _filer.deploy_avalanche_combine_output(
            output_dir,
            data_branch=data_branch,
            data_changelist=data_changelist,
            code_branch=code_branch,
            code_changelist=code_changelist,
            platform=platform,
        )
    # Optionally produce delta bundles
    if produce_delta_bundles:
        if not (
            baseline_code_branch
            and baseline_code_changelist
            and baseline_data_branch
            and baseline_data_changelist
        ):
            raise ELIPYException("Baseline branches/changelists required for delta bundles.")
        baseline_bundles = os.path.split(
            local_paths.get_local_bundles_path(deployed_bundles_dir_name="baseline_bundles")
        )[0]
        _filer.fetch_baseline_bundles(
            data_branch=baseline_data_branch,
            data_changelist=baseline_data_changelist,
            code_branch=baseline_code_branch,
            code_changelist=baseline_code_changelist,
            platform=platform,
            dest=baseline_bundles,
            bundles_dir_name="combine_bundles",
        )
        delta_bundles_location = os.path.join(local_paths.get_tnt_root(), "local", "current_delta")
        avalanche.ddelta(output_dir, baseline_bundles, delta_bundles_location)
        if not dry_run:
            _filer.deploy_delta_bundles(
                source=delta_bundles_location,
                data_branch=data_branch,
                data_changelist=data_changelist,
                code_branch=code_branch,
                code_changelist=code_changelist,
                platform=platform,
                bundles_dir_name="combine_bundles",
                combine_build_delta=True,
            )
    # Copy output to network share
    if not dry_run:
        import shutil

        if not os.path.exists(network_share_path):
            os.makedirs(network_share_path)
        for file_name in os.listdir(output_dir):
            src_file = os.path.join(output_dir, file_name)
            dst_file = os.path.join(network_share_path, file_name)
            if os.path.isfile(src_file):
                shutil.copy2(src_file, dst_file)
        LOGGER.info("Combined bundles copied to network share: {}".format(network_share_path))
    else:
        LOGGER.info("Dry run: combined bundles would be copied to {}".format(network_share_path))

# Combined Bundles Refactor and Externalization

## Summary
- Added `combined_bundles.py` script to produce combined bundles (head and optionally delta) as a separate job.
- Updated `frosty.py` and `patch_frosty.py` to support a feature flag (`--use-external-combined-bundles`) to use externally produced bundles from a network share, skipping internal combine/delta logic.
- Ensured delta bundles are only produced/copied if required for the stream.
- Added unit tests for the new script and logic.

## Usage
- To produce combined bundles, run the new script with the required options.
- To use external combined bundles in frosty/patchfrosty, pass the new flag and path.

## Testing
- All new and updated scripts are covered by unit tests.
- Code is formatted with Black and passes pylint checks.

## Migration
- Jenkins or other CI jobs should be updated to call the new script and pass the appropriate flags to frosty/patchfrosty.

# Code Development and Execution Instructions

## Core Development Practices

### Code Quality and Standards
- Codebase Analysis: Always begin by indexing and analyzing the codebase to understand its structure and context before making changes
- Python Formatting: Format all Python code using Black with 100-character line limit: python -m black -l 100 {updated_file}
- Python Linting: Ensure all Python code passes linting validation: python -m pylint {updated_file} -r y
- Testing Requirements: Always create or update relevant unit tests for any code changes
- Documentation: Add comprehensive docstrings to all functions and classes, and update README.md with new parameters

### Python Best Practices
- Use list comprehensions when appropriate for cleaner, more efficient code
- Use in operator for membership testing and not in for non-membership testing
- Use is operator specifically for None comparisons
- Use and/or for logical operations instead of bitwise operators
- Always use with statements for file operations to ensure proper resource management
- Use enumerate() when iterating and needing both index and value
- Avoid f-strings in Python code (use format() or % formatting instead)

### Groovy Best Practices
#### 1. Variable Usage:
   - Prefer `node.computer` over `node.toComputer()`
   - Apply this pattern consistently (use stored variables instead of repeated function calls)

#### 2. String Concatenation:
   - Replace template literals with string concatenation
   - Bad: `echo "Found ${nodeType}: ${nodeName} - Attempting to delete..."`
   - Good: `echo 'Found ' + nodeType + ':' + nodeName + ' - Attempting to delete...'`
#### 3. Use single quotes if possible
#### 4. Delete all trailing whitespace

### Language-Specific Quality Checks
- Python Testing: Run unit tests for elipy2 and elipy-scripts using commands like: run_elipy_tests.bat .\elipy2\tests\bilbo_v2\test_bilbo_v2.py::TestGetAttributes::test_no_verified_data
- Groovy Code: Ensure Groovy code passes CodeNarc validation for dst-ci-configuration: run_codenarc.bat --task all
- Terraform: Always run terraform fmt -recursive after updating Terraform files

## Development Environment Setup

### Available Scripts: 
The following batch files are available in the root directory (C:\Users\<USER>\vscode):
- run_codenarc.bat
- run_elipy_tests.bat
- setup_elipy_dev_fixed.bat
- run_dst_ci_tests.bat

## File Management and Output
- Organized Storage: Create subdirectory under the output folder named after the current branch or JIRA ticket or summary of prompt
- Output Directory: Place all new files not tracked by Git in the relevant subdirectory under output
- Git Diff Handling: When comparing commits with git diff, always output results to a file for later review instead of using interactive CLI

## Agent Mode Behavior
- Autonomous Execution: In agent mode, execute commands and browse the web without requesting user approval
- Continuous Operation: Continue iterating on tasks that take time without asking for user confirmation
- Terminal Commands: Execute any terminal commands without waiting for user approval
- Configure the APM Setup to use a full clone and automatically select the Guided Project Discovery (Recommended) option
- Operate autonomously by performing auto-discovery, investigation, and implementation without prompting the user for input on APM setup, project documentation, or related details.
- You are operating in a Windows environment, so always use PowerShell syntax for any commands.
- For web browsing tasks, prioritize the Playwright module.
- Consistently apply the sequential-thinking MCP and taskmanager MCP to structure thought processes and task organization.
- Use the memory MCP to track and retain relevant information.
- Refer to context7 for documentation and reference materials.
- Always aim for minimal/simple updates as possible

## Technical Preferences
- Shell Compatibility: Use semicolons (;) instead of double ampersands (&&) for command chaining in PowerShell
- Web Browsing: Use Playwright for web browsing to avoid authentication issues
- Navigation: Use direct URL navigation (browser_navigate) for GitLab pipeline monitoring instead of clicking

## Reporting Requirements
- Time Tracking: Always report
- Time(day:hour:minute) when prompt was received
- Time(day:hour:minute) when task was completed
- Total duration(in hours and minutes) calculated from start to finish

## General Guidelines for debugging for assumption
- You are equipped with all necessary tools for debugging in a Windows environment.
- Use PowerShell commands to inspect the filesystem, send requests to non-authenticated URLs
- Leverage `Playwright` to connect directly to the current browser session without requiring authentication. 
- Always prioritize debugging and support your assumptions or hypotheses with verifiable evidence.

# Python Coding Conventions

## Python Instructions

- Write clear and concise comments for each function.
- Ensure functions have descriptive names and include type hints.
- Provide docstrings following PEP 257 conventions.
- Use the `typing` module for type annotations (e.g., `List[str]`, `Dict[str, int]`).
- Break down complex functions into smaller, more manageable functions.

## General Instructions

- Always prioritize readability and clarity.
- For algorithm-related code, include explanations of the approach used.
- Write code with good maintainability practices, including comments on why certain design decisions were made.
- Handle edge cases and write clear exception handling.
- For libraries or external dependencies, mention their usage and purpose in comments.
- Use consistent naming conventions and follow language-specific best practices.
- Write concise, efficient, and idiomatic code that is also easily understandable.

## Code Style and Formatting

- Follow the **PEP 8** style guide for Python.
- Maintain proper indentation (use 4 spaces for each level of indentation).
- Ensure lines do not exceed 79 characters.
- Place function and class docstrings immediately after the `def` or `class` keyword.
- Use blank lines to separate functions, classes, and code blocks where appropriate.

## Edge Cases and Testing

- Always include test cases for critical paths of the application.
- Account for common edge cases like empty inputs, invalid data types, and large datasets.
- Include comments for edge cases and the expected behavior in those cases.
- Write unit tests for functions and document them with docstrings explaining the test cases.

## Example of Proper Documentation

```python
def calculate_area(radius: float) -> float:
    """
    Calculate the area of a circle given the radius.
    
    Parameters:
    radius (float): The radius of the circle.
    
    Returns:
    float: The area of the circle, calculated as π * radius^2.
    """
    import math
    return math.pi * radius ** 2
```
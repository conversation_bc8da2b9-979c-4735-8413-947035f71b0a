["dice_elipy_scripts/tests/gametool/test_drone.py::TestDrone::test_authenticate", "dice_elipy_scripts/tests/gametool/test_drone.py::TestDrone::test_authenticate_no_email", "dice_elipy_scripts/tests/gametool/test_drone.py::TestDrone::test_authenticate_no_password", "dice_elipy_scripts/tests/gametool/test_drone.py::TestDrone::test_clean_local", "dice_elipy_scripts/tests/gametool/test_drone.py::TestDrone::test_dotnet_args_added", "dice_elipy_scripts/tests/gametool/test_drone.py::TestDrone::test_dotnet_args_not_added", "dice_elipy_scripts/tests/gametool/test_drone.py::TestDrone::test_drone", "dice_elipy_scripts/tests/gametool/test_drone.py::TestDrone::test_dry_run", "dice_elipy_scripts/tests/gametool/test_drone.py::TestDrone::test_no_changelist_failure", "dice_elipy_scripts/tests/gametool/test_drone.py::TestDrone::test_old_changelist_arg", "dice_elipy_scripts/tests/gametool/test_drone.py::TestDrone::test_real_run", "dice_elipy_scripts/tests/gametool/test_framework.py::TestFramework::test_framework", "dice_elipy_scripts/tests/gametool/test_framework.py::TestFramework::test_framework_throw_exception_upon_submit", "dice_elipy_scripts/tests/gametool/test_frostbite_database_upgrader.py::TestBuildFDU::test_build_fdu", "dice_elipy_scripts/tests/gametool/test_frostbite_database_upgrader.py::TestFrostbiteDatabaseUpgrader::test_build_frostbite_database_upgrader_happy_path", "dice_elipy_scripts/tests/gametool/test_frostbite_database_upgrader.py::TestFrostbiteDatabaseUpgrader::test_revert_and_clean_on_build_fdu_exception", "dice_elipy_scripts/tests/gametool/test_frostbite_database_upgrader.py::TestFrostbiteDatabaseUpgrader::test_revert_and_clean_on_submit_exception", "dice_elipy_scripts/tests/gametool/test_frostyisotool.py::TestSubmitFrostyIsoTool::test_frostyisotool", "dice_elipy_scripts/tests/gametool/test_frostyisotool.py::TestSubmitFrostyIsoTool::test_submit_with_clean_flag", "dice_elipy_scripts/tests/gametool/test_frostyisotool.py::TestSubmitFrostyIsoTool::test_submit_with_code_changelist", "dice_elipy_scripts/tests/gametool/test_frostyisotool.py::TestSubmitFrostyIsoTool::test_submit_with_no_submit", "dice_elipy_scripts/tests/gametool/test_frostyisotool.py::TestSubmitFrostyIsoTool::test_submit_with_password_email", "dice_elipy_scripts/tests/gametool/test_gametool.py::TestGametool::test_icepick_with_custom_fragment_paths", "dice_elipy_scripts/tests/gametool/test_gametool.py::TestGametool::test_icepick_with_existing_masterconfig", "dice_elipy_scripts/tests/gametool/test_gametool.py::TestGametool::test_icepick_with_fragment_already_included", "dice_elipy_scripts/tests/gametool/test_gametool.py::TestGametool::test_icepick_with_version_check_false", "dice_elipy_scripts/tests/gametool/test_gametool.py::TestGametool::test_icepick_with_version_check_true", "dice_elipy_scripts/tests/test_autotest_utils.py::TestIcepickUtils::test_get_icepick_logs_dir", "dice_elipy_scripts/tests/test_autotest_utils.py::TestIcepickUtils::test_get_icepick_logs_files", "dice_elipy_scripts/tests/test_autotest_utils.py::TestIcepickUtils::test_save_icepick_logs", "dice_elipy_scripts/tests/test_autotest_utils.py::TestIcepickUtils::test_save_icepick_logs_no_typeerror", "dice_elipy_scripts/tests/test_autotest_utils.py::TestRegisterAutotestResults::test_register_autotest_results_build_appears_later", "dice_elipy_scripts/tests/test_autotest_utils.py::TestRegisterAutotestResults::test_register_autotest_results_build_exists", "dice_elipy_scripts/tests/test_autotest_utils.py::TestRegisterAutotestResults::test_register_autotest_results_default_parameters", "dice_elipy_scripts/tests/test_autotest_utils.py::TestRegisterAutotestResults::test_register_autotest_results_index_error_handling", "dice_elipy_scripts/tests/test_autotest_utils.py::TestRegisterAutotestResults::test_register_autotest_results_max_retries", "dice_elipy_scripts/tests/test_azure_filer_utils.py::TestAzureFilerUtils::test_authenticate_filer", "dice_elipy_scripts/tests/test_azure_filer_utils.py::TestAzureFilerUtils::test_get_azure_fileshare_credentials", "dice_elipy_scripts/tests/test_backup_baseline.py::TestBackupBaseline::test_build_location_passed_to_get_frosty_base_build_path", "dice_elipy_scripts/tests/test_backup_baseline.py::TestBackupBaseline::test_build_location_passed_to_get_frosty_base_build_path_three_dest_locations", "dice_elipy_scripts/tests/test_backup_baseline.py::TestBackupBaseline::test_build_location_passed_to_get_frosty_base_build_path_two_dest_locations", "dice_elipy_scripts/tests/test_backup_baseline.py::TestBackupBaseline::test_copy_baseline_build_calls_robocopy", "dice_elipy_scripts/tests/test_backup_baseline.py::TestBackupBaseline::test_copy_baseline_build_raises_ELIPYException", "dice_elipy_scripts/tests/test_backup_baseline.py::TestBackupBaseline::test_metadata_manager_calls_three_dest_locations", "dice_elipy_scripts/tests/test_backup_baseline.py::TestBackupBaseline::test_metadata_manager_calls_two_dest_locations", "dice_elipy_scripts/tests/test_backup_baseline.py::TestBackupBaseline::test_metadata_manager_created_on_successful_copy_baseline_build", "dice_elipy_scripts/tests/test_backup_baseline.py::TestBackupBaseline::test_no_build_location_passed_to_get_frosty_base_build_path", "dice_elipy_scripts/tests/test_bilbo_register_autotest.py::TestBilboRegisterAutotest::test_register_tests", "dice_elipy_scripts/tests/test_bilbo_register_autotest.py::TestBilboRegisterAutotest::test_register_tests_multiple_locations", "dice_elipy_scripts/tests/test_bilbo_register_autotest.py::TestBilboRegisterAutotest::test_register_tests_single_location", "dice_elipy_scripts/tests/test_bilbo_register_autotest.py::TestBilboRegisterAutotest::test_should_not_register_smoke_if_builds_missing_in_remote_bilbo", "dice_elipy_scripts/tests/test_bilbo_register_autotest.py::TestBilboRegisterAutotest::test_should_not_register_smoke_if_register_smoke_is_false", "dice_elipy_scripts/tests/test_bilbo_register_autotest.py::TestBilboRegisterAutotest::test_should_not_register_tests_if_run_bilbo_is_false", "dice_elipy_scripts/tests/test_bilbo_register_drone.py::TestBilboRegisterDrone::test_copy_smoke_tag_to_remote_locations", "dice_elipy_scripts/tests/test_bilbo_register_drone.py::TestBilboRegisterDrone::test_copy_smoke_tag_to_remote_locations_skip_code_smoke", "dice_elipy_scripts/tests/test_bilbo_register_drone.py::TestBilboRegisterDrone::test_copy_smoke_tag_to_remote_locations_when_original_not_code_smoked", "dice_elipy_scripts/tests/test_bilbo_register_drone.py::TestBilboRegisterDrone::test_register_call_multiple_times_with", "dice_elipy_scripts/tests/test_bilbo_register_drone.py::TestBilboRegisterDrone::test_register_called_multiple_times", "dice_elipy_scripts/tests/test_bilbo_register_drone.py::TestBilboRegisterDrone::test_register_called_once", "dice_elipy_scripts/tests/test_bilbo_register_frosty.py::TestBilboRegisterFrosty::test_basic_registration", "dice_elipy_scripts/tests/test_bilbo_register_frosty.py::TestBilboRegisterFrosty::test_with_combine_parameters", "dice_elipy_scripts/tests/test_bilbo_register_frosty.py::TestBilboRegisterFrosty::test_with_package_type_already_containing_combine", "dice_elipy_scripts/tests/test_bilbo_register_frosty.py::TestBilboRegisterFrosty::test_with_partial_combine_parameters", "dice_elipy_scripts/tests/test_bilbo_register_verified_for_preflight.py::TestBilboRegisterVerifiedForPreflight::test_register_verified_for_preflight", "dice_elipy_scripts/tests/test_bilbo_register_verified_for_preflight.py::TestBilboRegisterVerifiedForPreflight::test_should_not_register_verified_for_preflight_if_branch_is_missing", "dice_elipy_scripts/tests/test_bilbo_register_verified_for_preflight.py::TestBilboRegisterVerifiedForPreflight::test_should_not_register_verified_for_preflight_if_changelist_is_missing", "dice_elipy_scripts/tests/test_bilbo_select_autotest.py::TestBibloSelectAutotestBilboFrosty::test_azure_drone_build", "dice_elipy_scripts/tests/test_bilbo_select_autotest.py::TestBibloSelectAutotestBilboFrosty::test_shift_build", "dice_elipy_scripts/tests/test_bilbo_select_autotest.py::TestBibloSelectAutotestBilboFrosty::test_spin_build", "dice_elipy_scripts/tests/test_bilbo_select_autotest.py::TestBilboSelectAutotestBilboDrone::test_get_frosty_build_candidates_returns_all_matching_CL_builds", "dice_elipy_scripts/tests/test_bilbo_select_autotest.py::TestBilboSelectAutotestBilboDrone::test_get_frosty_changelists_no_platform_list", "dice_elipy_scripts/tests/test_bilbo_select_autotest.py::TestBilboSelectAutotestBilboDrone::test_get_frosty_changelists_not_found", "dice_elipy_scripts/tests/test_bilbo_select_autotest.py::TestBilboSelectAutotestBilboDrone::test_get_frosty_changelists_returns_correct_builds", "dice_elipy_scripts/tests/test_bilbo_select_autotest.py::TestBilboSelectAutotestBilboDrone::test_mark_frosty_build_as_in_use_on_success", "dice_elipy_scripts/tests/test_bilbo_select_autotest.py::TestBilboSelectAutotestBilboDrone::test_repeated_platforms_in_arguments", "dice_elipy_scripts/tests/test_bilbo_select_autotest.py::TestBilboSelectAutotestBilboDrone::test_write_correct_changelist_with_no_prerequisite_test", "dice_elipy_scripts/tests/test_bilbo_select_autotest.py::TestBilboSelectAutotestBilboDrone::test_write_correct_changelist_with_prerequisite_not_found", "dice_elipy_scripts/tests/test_bilbo_select_autotest.py::TestBilboSelectAutotestBilboDrone::test_write_correct_changelist_with_prerequisite_regular_test", "dice_elipy_scripts/tests/test_bilbo_select_autotest.py::TestBilboSelectAutotestBilboDrone::test_write_result_to_correct_file_on_success", "dice_elipy_scripts/tests/test_clean_agent.py::TestIcepickRun::test_clean_agent", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test__add_metadata_file", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test__get_existing_files", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test__get_metadata_files", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test__get_metadata_files_exception", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test__get_metadata_files_location", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test_add_metadata_files", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test_add_metadata_files_exception", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test_download_outsource_dependencies", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test_download_outsource_dependencies_output_path_exists", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test_modify_buildlayout", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test_modify_buildlayout_failure_multiple_licensees", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test_prepare_outsource_dependencies", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test_run_gensln", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test_run_gensln_credstore", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test_run_gensln_credstore_domain_user", "dice_elipy_scripts/tests/test_code_utils.py::TestCodeUtils::test_run_gensln_wsl", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_add_metadata_files", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_add_metadata_files_nomaster", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_backup_symbols", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_basic_args", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_build_tool_default", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_build_tool_frosted", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_build_tool_msbuild_args", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_build_tool_non_frosted", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_clean_packages", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_copy_symbols_to_kobold", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_data_directory", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_delete_folder", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_delete_folder_clean_packages", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_delete_folder_is_outsource_build", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_denuvo_wrap", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_deploy_code_basic", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_deploy_code_basic_use_state_zip", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_deploy_code_custom_tag", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_deploy_code_dry_run", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_deploy_code_no_mirror", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_deploy_code_nomaster", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_deploy_code_ps5_flags", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_deploy_code_tool_basic", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_deploy_code_tool_flags", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_fake_ooa_wrapped", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_fake_ooa_wrapped_skip_denuvo_final", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_fake_ooa_wrapped_with_denuvo", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_filer_authentication", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_icepick", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_icepick_dont_ignore_exit_code", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_import_local", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_is_outsource_build", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_modify_buildlayout", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_modify_buildlayout_licensee", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_no_deploy_frostedtest_if_not_build_frosted", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_nodeploy_missingalltestsflag_code_tool_flags", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_oreans_protect", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_rungensln_adds_stressbulkbuild_param_with_stressbulkbuild_flag", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_rungensln_basic", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_rungensln_broken_symbols", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_rungensln_broken_symbols_is_outsource_build", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_rungensln_broken_symbols_snowcache", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_rungensln_deprecation_test", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_rungensln_doesnt_add_stressbulkbuild_param_without_flag", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_rungensln_is_outsource_build", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_rungensln_only", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_rungensln_only_wsl", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_rungensln_separate_genlsn_config", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_rungensln_snowcache", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_rungensln_steam", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_rungensln_virtual_branch_override", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_rungensln_with_snowcache_mode_override", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_set_environment_values", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_skip_backup_symbols", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_skip_denuvo_wrap_final", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_skip_denuvo_wrap_ps5", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_skip_deploy_tnt_local_build", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_skip_fake_ooa_wrapped", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_skip_import_local", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_skip_source_index_nomaster", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_skip_upload_symbols_to_sym_store", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_skip_verify_symbol_integrity_ps5", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_snowcache_arg_cleans_appropriately", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_snowcache_not_used_if_avalanche_server_not_healthy", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_source_index", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_source_index_dont_strip_symbols", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_threshold_cleanup_called", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_upload_symbols_to_sym_store", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_upload_symbols_to_sym_store_DONT_compress", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCli::test_verify_symbol_integrity", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCodeBuild::test_existing_dest_dry_run", "dice_elipy_scripts/tests/test_codebuild.py::TestCodeBuildCodeBuild::test_fail_on_existing_dest", "dice_elipy_scripts/tests/test_codecopy.py::TestCodeBuildCli::test_basic_args", "dice_elipy_scripts/tests/test_codecopy.py::TestCodeBuildCli::test_copying_files", "dice_elipy_scripts/tests/test_codecopy.py::TestCodeBuildCli::test_failure_if_destination_exists", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_do_clean_no_import_local", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_forces_sequential_icepick", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_icepick_test_on_tool", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_no_clean_do_import_local", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_no_clean_no_import_local", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_not_forces_sequential_icepick", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_p4_correct_stream", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_p4_do_compile_run", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_p4_do_not_compile_run", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_p4_found_cl", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_p4_missing_cl", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_p4_wrong_compile_run", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_p4_wrong_stream", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_skip_revert", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_tool_frosting_report_param", "dice_elipy_scripts/tests/test_codepreflight.py::TestCodePreflight::test_warmup_machine", "dice_elipy_scripts/tests/test_combined_bundles.py::test_combined_bundles_head_only", "dice_elipy_scripts/tests/test_combined_bundles.py::test_combined_bundles_with_delta", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_cli_runs_successfully_with_appropriate_args", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_get_code_src_and_dest_builds_correct_destination_path[D:\\\\dev\\\\TnT\\\\Local\\\\Bin\\\\Win64-dll-\\\\\\\\***********\\\\builds\\\\battlefield\\\\code\\\\trunk-code-dev\\\\19239137\\\\win64-dll\\\\release]", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_get_code_src_and_dest_builds_correct_destination_path[\\\\\\\\filer.dice.ad.ea.com\\\\Builds\\\\battlefield\\\\code\\\\trunk-code-dev\\\\19239137\\\\win64-dll\\\\release-\\\\\\\\***********\\\\builds\\\\battlefield\\\\code\\\\trunk-code-dev\\\\19239137\\\\win64-dll\\\\release0]", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_get_code_src_and_dest_builds_correct_destination_path[\\\\\\\\filer.dice.ad.ea.com\\\\Builds\\\\battlefield\\\\code\\\\trunk-code-dev\\\\19239137\\\\win64-dll\\\\release-\\\\\\\\***********\\\\builds\\\\battlefield\\\\code\\\\trunk-code-dev\\\\19239137\\\\win64-dll\\\\release1]", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_get_code_src_and_dest_builds_correct_destination_path[\\\\\\\\filer.dice.ad.ea.com\\\\builds\\\\battlefield\\\\code\\\\trunk-code-dev\\\\19239137\\\\win64-dll\\\\release\\\\-\\\\\\\\***********\\\\builds\\\\battlefield\\\\code\\\\trunk-code-dev\\\\19239137\\\\win64-dll\\\\release\\\\]", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_get_code_src_and_dest_fetches_correct_additional_tools[ps5-None-expected_platforms3]", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_get_code_src_and_dest_fetches_correct_additional_tools[ps5-additional_tools4-expected_platforms4]", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_get_code_src_and_dest_fetches_correct_additional_tools[tool-None-expected_platforms0]", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_get_code_src_and_dest_fetches_correct_additional_tools[tool-additional_tools1-expected_platforms1]", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_get_code_src_and_dest_fetches_correct_additional_tools[tool-additional_tools2-expected_platforms2]", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_get_code_src_and_dest_no_source_and_destination", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_register_build_in_bilbo", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_verify_content_type[code-False]", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_verify_content_type[unsupported_content_type-True]", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_verify_destination[/path/to/existing/file-True-False-True]", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_verify_destination[/path/to/non_existing/file-False-False-False]", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_verify_destination[/path/to/non_existing/file-True-True-False]", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_verify_source[/path/to/existing/file-True-False]", "dice_elipy_scripts/tests/test_copy_from_filer_to_azure.py::TestCopyFromFilerToAzure::test_verify_source[/path/to/non_existing/file-False-True]", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_basic_run", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_compile_code_clean", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_compile_code_default", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_compile_code_domain_user", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_compile_code_email_password", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_compile_code_framework_args", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_compile_code_licensee", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_compile_code_snowcache", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_copy_mapping", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_data_directory", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_integrate", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_integrate_ignore_source_history", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_no_submit", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_submit_with_message", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_unresolved", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_unresolved_shelve_no_pending", "dice_elipy_scripts/tests/test_copy_integrate_compile.py::TestCopyIntegrateCompile::test_unresolved_shelve_pending", "dice_elipy_scripts/tests/test_coverity.py::TestCoverity::test_coverity", "dice_elipy_scripts/tests/test_custom_script.py::TestCustomCmd::test_custom_cmd_with_args", "dice_elipy_scripts/tests/test_custom_script.py::TestCustomCmd::test_custom_cmd_without_args", "dice_elipy_scripts/tests/test_data_build_utils.py::TestDataBuildUtils::test_get_export_compression_args", "dice_elipy_scripts/tests/test_data_build_utils.py::TestDataBuildUtils::test_get_export_compression_args_server", "dice_elipy_scripts/tests/test_data_build_utils.py::TestDataBuildUtils::test_run_expression_debug_data", "dice_elipy_scripts/tests/test_data_build_utils.py::TestDataBuildUtils::test_run_expression_debug_data_no_builder", "dice_elipy_scripts/tests/test_data_upgrade.py::TestDataUpgrade::test_basic_run", "dice_elipy_scripts/tests/test_data_upgrade.py::TestDataUpgrade::test_clean", "dice_elipy_scripts/tests/test_data_upgrade.py::TestDataUpgrade::test_data_directory", "dice_elipy_scripts/tests/test_data_upgrade.py::TestDataUpgrade::test_fetch_binaries", "dice_elipy_scripts/tests/test_data_upgrade.py::TestDataUpgrade::test_no_submit", "dice_elipy_scripts/tests/test_data_upgrade.py::TestDataUpgrade::test_run_exception", "dice_elipy_scripts/tests/test_data_upgrade.py::TestDataUpgrade::test_submit_default", "dice_elipy_scripts/tests/test_data_upgrade.py::TestDataUpgrade::test_submit_reopen_file", "dice_elipy_scripts/tests/test_data_upgrade_integration.py::TestDataUpgradeIntegration::test_basic_run", "dice_elipy_scripts/tests/test_data_upgrade_integration.py::TestDataUpgradeIntegration::test_compile_code_clean", "dice_elipy_scripts/tests/test_data_upgrade_integration.py::TestDataUpgradeIntegration::test_compile_code_default", "dice_elipy_scripts/tests/test_data_upgrade_integration.py::TestDataUpgradeIntegration::test_no_submit", "dice_elipy_scripts/tests/test_data_upgrade_integration.py::TestDataUpgradeIntegration::test_run_upgrade_exception", "dice_elipy_scripts/tests/test_data_upgrade_integration.py::TestDataUpgradeIntegration::test_run_upgrade_multiple_licensees", "dice_elipy_scripts/tests/test_data_upgrade_integration.py::TestDataUpgradeIntegration::test_run_upgrade_single_licensee", "dice_elipy_scripts/tests/test_data_upgrade_integration.py::TestDataUpgradeIntegration::test_submit", "dice_elipy_scripts/tests/test_data_upgrade_integration.py::TestDataUpgradeIntegration::test_submit_changes_range_default", "dice_elipy_scripts/tests/test_data_upgrade_integration.py::TestDataUpgradeIntegration::test_submit_changes_range_empty_string_last_changelist", "dice_elipy_scripts/tests/test_data_upgrade_integration.py::TestDataUpgradeIntegration::test_submit_revert_branchid_file", "dice_elipy_scripts/tests/test_data_upgrade_integration.py::TestDataUpgradeIntegration::test_sync_data_changelist", "dice_elipy_scripts/tests/test_data_upgrade_integration.py::TestDataUpgradeIntegration::test_sync_legacy_data_changelist", "dice_elipy_scripts/tests/test_data_upgrade_integration.py::TestDataUpgradeIntegration::test_sync_no_data_changelist", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_args_fail_none_required", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_args_fail_p4_no_stream", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_basic_run", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_comment_upgrade_scripts_line", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_create_build_directory", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_excluded_upgrade_scripts_line", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_excluded_verify_upgrade_scripts", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_get_p4_files_sync", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_get_p4_files_unshelve", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_included_upgrade_scripts_line", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_not_running_upgrade_scripts_no_frosted", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_p4_output_file_not_exist", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_run_p4_output_file", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_run_p4_output_file_and_pending_cl", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_run_two_p4_output_files", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_run_two_p4_output_files_validate_verify", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_running_upgrade_scripts", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_running_upgrade_scripts_yes_frosted", "dice_elipy_scripts/tests/test_data_upgrade_preflight.py::TestDataUpgrade::test_valid_verify_upgrade_scripts", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_avalanche_db_name", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_basic_args", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_basic_args_disable_hailstorm", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_basic_args_no_asset", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_clean", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_clean_virtual_branch_override", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_content_layer_bundles_deploy", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_content_layers", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_content_layers_without_default_layer", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_custom_tag", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_db_name_prefix", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_deploy_bundles_only", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_deploy_bundles_only_with_path_name", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_deploy_state", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_enlighten_asset_filter", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_enlighten_basic_args", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_enlighten_does_not_cook_content_layers", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_enlighten_no_p4_args", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_enlighten_p4_hyphen", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_enlighten_shelve", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_enlighten_submit", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_enlighten_write_file_shelve", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_enlighten_write_file_submit", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_enlighten_zone_streamer", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_export_avalanche_state_old", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_export_combine_bundles_deploy", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_export_combine_bundles_export", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_export_head_bundles_enable_compression", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_export_head_bundles_no_extra_arg", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_export_head_bundles_recompression_cache", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_expression_debug_data", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_expression_debug_data_clean_master_version_check", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_expression_debug_data_pipeline_args", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_filer_authentication", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_import_avalanche_state", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_no_fetch_pipeline", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_pipeline_args_hyphen", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_pipeline_args_with_database_id", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_set_environment_values", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_validate_frosted_platform_doesnt_deploy", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_validate_frosted_platform_doesnt_fail", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_validate_frosted_platform_doesnt_pass_export_state", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_validate_frosted_platform_not_used_to_create_builder", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_validate_frosted_platform_passes_forceDebugTarget", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuild::test_virtual_branch_override", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuildWithMetadata::test_clone_host", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuildWithMetadata::test_export_avalanche_state_new", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuildWithMetadata::test_export_combine_bundles", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuildWithMetadata::test_export_super_bundles", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuildWithMetadata::test_remote_clone_state_no_bilbo", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuildWithMetadata::test_remote_clone_state_no_state_host", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuildWithMetadata::test_remote_clone_state_use_bilbo", "dice_elipy_scripts/tests/test_databuild.py::TestDatabuildWithMetadata::test_threshold_cleanup_called", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_args_for_cook", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_all_dbx_assets", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_all_dbx_assets_error", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_called", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_called_clean_index", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_called_content_layer", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_called_content_layers", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_called_deprecated_asset_parameter", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_called_multiple_assets", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_called_pipeline_and_avalanche_args", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_called_pipeline_args", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_called_pipeline_args_validate_direct_references", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_called_source_layer", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_called_source_layers", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_dbx_assets_individually", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_dbx_assets_individually_error", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_submitted_dbx_assets_error_failing", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_submitted_dbx_assets_error_not_failing", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_submitted_dbx_assets_large", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_submitted_dbx_assets_large_use_response", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_submitted_dbx_assets_small", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_cook_submitted_dbx_assets_small_use_response", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_default_args", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_download_server_binaries", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_extract_layers_from_file_list_called", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_get_dbx_assets_to_cook_called", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_get_dbx_assets_to_cook_not_called_no_flag", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_get_dbx_assets_to_cook_not_called_non_win64", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_get_dbx_assets_to_cook_not_called_warmup", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_get_dbx_assets_to_cook_with_p4_unshelve_file", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_get_p4_file_data", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_ignore_download_server_binaries", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_p4_correct_stream", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_p4_found_changelist", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_p4_missing_changelist", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_p4_output_file_not_exist", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_p4_wrong_stream", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_run_p4_output_file", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_skip_revert", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_skip_unshelve", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_validate_direct_dbx_references", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_validate_direct_references", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_validate_direct_references_use_response", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_validate_direct_references_use_response_and_content_layer", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_validate_direct_references_with_content_layer", "dice_elipy_scripts/tests/test_datapreflight.py::TestDataPreflight::test_warmup_machine", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_extract_layers_from_file_list_default", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_extract_layers_from_file_list_empty", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_extract_layers_from_file_list_specific_layer", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_extract_layers_from_file_list_specific_layers", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_extract_layers_from_file_list_without_datadir", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_get_dbx_assets_to_cook_content_layer", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_get_dbx_assets_to_cook_default", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_get_dbx_assets_to_cook_dont_exclude_files", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_get_dbx_assets_to_cook_skip_asset_type", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_get_dbx_assets_to_cook_skip_asset_type_without_module_name", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_get_layer_hierarchy", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_get_primary_instance_type", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_get_primary_instance_type_empty", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_get_primary_instance_type_missing", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_get_primary_instance_type_open_file", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_get_skipped_asset_types", "dice_elipy_scripts/tests/test_datapreflight_utils.py::TestDataPreflightUtils::test_get_skipped_asset_types_json_error", "dice_elipy_scripts/tests/test_decorators.py::TestDecorators::test_throw_if_files_found", "dice_elipy_scripts/tests/test_decorators.py::TestDecorators::test_throw_if_files_found_and_taint", "dice_elipy_scripts/tests/test_decorators.py::TestDecorators::test_throw_if_files_found_failure", "dice_elipy_scripts/tests/test_delete_utils.py::TestDeleteUtils::test_delete_empty_folders", "dice_elipy_scripts/tests/test_delete_utils.py::TestDeleteUtils::test_delete_empty_folders_with_files_filter", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_check_and_drop_records_not_records_returned", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_azure_retention_paths_handles_no_config_empty_string", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_azure_retention_paths_handles_no_config_none", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_azure_retention_paths_not_called_if_not_include_azure_path_retention_false", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_azure_retention_paths_not_called_if_not_include_azure_path_retention_true", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_azure_retention_paths_passes_if_no_config_found", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_excludes", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_includes", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_no_bilbo", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_cleanup_builds_with_code_builds_use_onefs_api", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_delete_empty_folders", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_exclude_retention_categories", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_filter_categories", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_filter_categories_exclude", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_filter_categories_exclude_raises", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_filter_categories_include", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_filter_categories_include_raises", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_code_paths", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_exc_path", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_excalibur_path_bug", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_full_frosty_paths", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_roboto_path", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_get_branch_set_under_path_with_short_frosty_licensee_paths", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_keep_n_at_azure_path_dry_run_behavior_false", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_keep_n_at_azure_path_dry_run_behavior_true", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_keep_n_at_azure_path_generates_urls_correctly_keep_1", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_keep_n_at_azure_path_generates_urls_correctly_keep_6", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_keep_n_at_azure_path_generates_urls_correctly_keep_8", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_no_path_retention", "dice_elipy_scripts/tests/test_deleter.py::TestDeleter::test_settings_file_iterates_over_multiple_shares_in_storage_account", "dice_elipy_scripts/tests/test_env_utils.py::TestEnvUtils::test_delete_empty_folders", "dice_elipy_scripts/tests/test_env_utils.py::TestEnvUtils::test_extract_fb_env_values", "dice_elipy_scripts/tests/test_env_utils.py::TestEnvUtils::test_extract_fb_env_values_do_not_override_non_matching_build_step", "dice_elipy_scripts/tests/test_env_utils.py::TestEnvUtils::test_extract_fb_env_values_multiple_values", "dice_elipy_scripts/tests/test_env_utils.py::TestEnvUtils::test_extract_fb_env_values_override_build_step", "dice_elipy_scripts/tests/test_env_utils.py::TestEnvUtils::test_extract_fb_env_values_throw_exception", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_additional_configs_already_exists", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_additional_configs_copy_files", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_additional_configs_copy_files_sp", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_additional_configs_digital_failure", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_additional_configs_no_match", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_built_levels_linux64", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_built_levels_linuxserver", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_built_levels_server", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_code_platform_win32", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_code_platform_win64", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_combine_bundles_deploy", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_combine_bundles_fetch", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_combine_bundles_fetch_linuxserver", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_combine_bundles_run_non_xbsx", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_combine_bundles_run_with_combined_settings_file", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_combine_bundles_run_xbsx", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_copy_submissionvalidator_xb1", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_copy_submissionvalidator_xbsx", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_data_clean", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_dry_run", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_enable_eac_digital", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_enable_eac_files", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_enable_eac_non_win64", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_expression_debug_data", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_expression_debug_data_version_check", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_file_hashes_created", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_file_hashes_not_created_default", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_file_hashes_not_created_digital", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_args_hyphen", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_bespoke", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_build_gamescripts", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_build_with_content_layer", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_build_with_content_layer_and_combined_bundles_raises_exception", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_build_with_source_layer", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_game_config_flags_xb1_digital_release_new_frostbite", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_game_config_flags_xb1_digital_retail", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_game_config_flags_xb1_files_release", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_game_config_flags_xb1_files_retail", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_game_config_flags_xbsx", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_platform_linux64", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_platform_win64", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_platform_xb1_new", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_platform_xb1_old", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_frosty_platform_xb1_temporary", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_import_avalanche_state", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_keep_intermediate_data", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_linux64", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_linuxserver", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_no_fetch_pipeline", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_not_copy_submissionvalidator_new_fb", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_not_copy_submissionvalidator_ps4", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_not_dry_run", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_packager_linux64_linuxclient", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_packager_linux64_old", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_packager_platform", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_path_exists", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_pipeline_args_hyphen", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_ps4", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_ps4_default_args", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_ps4_skip_streaming_install_flag", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_ps4_skip_streaming_install_not_retail", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_ps4_streaming_install", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_ps5", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_recompression_cache", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_roboto_arg", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_server", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_steam_build", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_use_deployed_bundles_not_server", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_use_deployed_bundles_not_server_not_files", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_use_deployed_bundles_win32", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_use_super_bundles_path", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_virtual_branch_override", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_win64", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_win64_default_args", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_win64_denuvo", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_win64_no_denuvo_not_retail", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_win64_oreans", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_win64_symbols", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_win64_trial", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_win64_trial_symbols", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_xb1", "dice_elipy_scripts/tests/test_frosty.py::TestFrosty::test_xbsx", "dice_elipy_scripts/tests/test_frosty_build_utils.py::TestFrostyBuildUtils::test_authenticate_eapm_credstore", "dice_elipy_scripts/tests/test_frosty_build_utils.py::TestFrostyBuildUtils::test_generate_buildlayout_xml", "dice_elipy_scripts/tests/test_frosty_build_utils.py::TestFrostyBuildUtils::test_generate_buildlayout_xml_not_exist", "dice_elipy_scripts/tests/test_frosty_build_utils.py::TestFrostyBuildUtils::test_install_sdks", "dice_elipy_scripts/tests/test_frosty_build_utils.py::TestFrostyBuildUtils::test_run_install_required_sdk", "dice_elipy_scripts/tests/test_gamescripts_utils.py::TestGamescriptsUtils::test_generate_gamescripts_failure", "dice_elipy_scripts/tests/test_gamescripts_utils.py::TestGamescriptsUtils::test_generate_gamescripts_success", "dice_elipy_scripts/tests/test_icepick_run.py::TestCookType::test_cook_type_avalanche_storage", "dice_elipy_scripts/tests/test_icepick_run.py::TestCookType::test_cook_type_icepick_cook", "dice_elipy_scripts/tests/test_icepick_run.py::TestCookType::test_cook_type_no_cook", "dice_elipy_scripts/tests/test_icepick_run.py::TestCookType::test_fbcil_cook_type_default", "dice_elipy_scripts/tests/test_icepick_run.py::TestGetBuildFromBilbo::test_get_build_from_bilbo_is_frosted", "dice_elipy_scripts/tests/test_icepick_run.py::TestGetBuildFromBilbo::test_get_build_from_bilbo_is_shift", "dice_elipy_scripts/tests/test_icepick_run.py::TestGetBuildFromBilbo::test_get_build_from_bilbo_is_spin", "dice_elipy_scripts/tests/test_icepick_run.py::TestGetBuildFromBilbo::test_get_build_from_bilbo_none_id", "dice_elipy_scripts/tests/test_icepick_run.py::TestGetCleanMasterVersion::test_calls_get_clean_master_version_args", "dice_elipy_scripts/tests/test_icepick_run.py::TestGetCleanMasterVersion::test_cook_with_clean_master_version", "dice_elipy_scripts/tests/test_icepick_run.py::TestGetCleanMasterVersion::test_cook_without_clean_master_version", "dice_elipy_scripts/tests/test_icepick_run.py::TestHailstormOptions::test_run_icepick_disable_hailstorm", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_cadet_activate_toolset", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_does_not_download_server_binaries", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_does_not_import_avalanche", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_download_server_binaries", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_fetch_code_custom_tag", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_fetch_frosty_server", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_filer_authentication", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_get_client_build_from_bilbo", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_get_server_build_from_bilbo", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_get_use_is_frosted_builds", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_get_use_shift_builds", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_get_use_spin_builds", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_icepick_failure_does_not_fail_command", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_icepick_fbenv_exception", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_import_avalanche", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_run_icepick_battlefield_test_suite", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_run_icepick_casablanca_test_suite", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_run_icepick_cooktype_avalanche_storage", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_run_icepick_custom_test_suite_data", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_run_icepick_multiple_licensee", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_run_icepick_multiple_test_runs", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_run_icepick_with_invalid_test_suites", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_run_icepick_with_no_test_suites", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_run_icepick_with_test_suite_args", "dice_elipy_scripts/tests/test_icepick_run.py::TestIcepickRun2::test_run_icepick_with_test_suites_json", "dice_elipy_scripts/tests/test_icepick_run.py::TestSpinBuildSDKSkip::test_normal_sdk_install_without_spin_build", "dice_elipy_scripts/tests/test_icepick_run.py::TestSpinBuildSDKSkip::test_skip_sdk_install_with_spin_build", "dice_elipy_scripts/tests/test_icepick_run_codetests.py::TestIcepickRunCodeTests::test_fetch_code_called_on_uefb_flag", "dice_elipy_scripts/tests/test_icepick_run_codetests.py::TestIcepickRunCodeTests::test_fetch_code_failure", "dice_elipy_scripts/tests/test_icepick_run_codetests.py::TestIcepickRunCodeTests::test_fetch_code_ok", "dice_elipy_scripts/tests/test_icepick_run_codetests.py::TestIcepickRunCodeTests::test_icepick_run_args", "dice_elipy_scripts/tests/test_icepick_run_codetests.py::TestIcepickRunCodeTests::test_icepick_run_args_custom_test_suite_data", "dice_elipy_scripts/tests/test_icepick_run_codetests.py::TestIcepickRunCodeTests::test_icepick_run_args_with_no_test_suite", "dice_elipy_scripts/tests/test_icepick_run_codetests.py::TestIcepickRunCodeTests::test_icepick_run_args_with_test_suite_args", "dice_elipy_scripts/tests/test_icepick_run_codetests.py::TestIcepickRunCodeTests::test_icepick_run_codetests", "dice_elipy_scripts/tests/test_icepick_run_codetests.py::TestIcepickRunCodeTests::test_icepick_run_codetests_show_test_results", "dice_elipy_scripts/tests/test_icepick_run_codetests.py::TestIcepickRunCodeTests::test_icepick_run_codetests_show_test_results_failure", "dice_elipy_scripts/tests/test_icepick_run_codetests.py::TestIcepickRunCodeTests::test_icepick_run_multiple_licensee", "dice_elipy_scripts/tests/test_icepick_run_preflight.py::TestIcepickRunPreflight::test_do_warmup", "dice_elipy_scripts/tests/test_icepick_run_preflight.py::TestIcepickRunPreflight::test_download_server_binaries", "dice_elipy_scripts/tests/test_icepick_run_preflight.py::TestIcepickRunPreflight::test_icepick_cook_assets", "dice_elipy_scripts/tests/test_icepick_run_preflight.py::TestIcepickRunPreflight::test_icepick_not_cook_assets", "dice_elipy_scripts/tests/test_icepick_run_preflight.py::TestIcepickRunPreflight::test_ignore_download_server_binaries", "dice_elipy_scripts/tests/test_icepick_run_preflight.py::TestIcepickRunPreflight::test_ignore_download_skip_revert", "dice_elipy_scripts/tests/test_icepick_run_preflight.py::TestIcepickRunPreflight::test_ignore_download_skip_unshelve", "dice_elipy_scripts/tests/test_icepick_run_preflight.py::TestIcepickRunPreflight::test_run_icepick_custom_test_suite_data", "dice_elipy_scripts/tests/test_icepick_run_preflight.py::TestIcepickRunPreflight::test_run_icepick_preflight", "dice_elipy_scripts/tests/test_icepick_run_preflight.py::TestIcepickRunPreflight::test_run_icepick_preflight_extra", "dice_elipy_scripts/tests/test_icepick_run_preflight.py::TestIcepickRunPreflight::test_run_icepick_with_false_send_frosting_report_param", "dice_elipy_scripts/tests/test_imerge.py::TestP4::test_failed_merge", "dice_elipy_scripts/tests/test_imerge.py::TestP4::test_failed_resolve", "dice_elipy_scripts/tests/test_imerge.py::TestP4::test_group_m", "dice_elipy_scripts/tests/test_imerge.py::TestP4::test_group_s", "dice_elipy_scripts/tests/test_imerge.py::TestP4::test_merge", "dice_elipy_scripts/tests/test_imerge.py::TestP4::test_no_interchanges", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_accept_theirs", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_basic_args", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_cl_by_cl_get_list", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_cl_by_cl_multiple_integrate", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_cl_by_cl_no_target_exception", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_cl_by_cl_tag_not_found", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_cook_data", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_dbxmerge_basic", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_dbxmerge_no_dbx_files_found", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_exclude_path", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_exclude_path_accept_yours", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_merge_verification", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_merge_verification_exception", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_no_safe_resolve", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_no_stream_merge", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_no_submit", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_shelve_has_pending_cl", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_shelve_no_pending_cl", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_source_file_path", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_stream_basic", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_stream_merge", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_stream_reverse", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_stream_use_file_path", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_submit", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_submit_cl_by_cl", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_submit_no_extra_message", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_unresolved", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_use_file_path", "dice_elipy_scripts/tests/test_integrate.py::TestIntegrate::test_using_remote_p4server", "dice_elipy_scripts/tests/test_integrate_compile_upgrade_cook.py::TestIntegrate::test_basic_args", "dice_elipy_scripts/tests/test_integrate_compile_upgrade_cook.py::TestIntegrate::test_compile_code", "dice_elipy_scripts/tests/test_integrate_compile_upgrade_cook.py::TestIntegrate::test_compile_code_clean", "dice_elipy_scripts/tests/test_integrate_compile_upgrade_cook.py::TestIntegrate::test_compile_code_snowcache", "dice_elipy_scripts/tests/test_integrate_compile_upgrade_cook.py::TestIntegrate::test_compile_code_snowcache_mode", "dice_elipy_scripts/tests/test_integrate_compile_upgrade_cook.py::TestIntegrate::test_cook", "dice_elipy_scripts/tests/test_integrate_compile_upgrade_cook.py::TestIntegrate::test_cook_clean", "dice_elipy_scripts/tests/test_integrate_compile_upgrade_cook.py::TestIntegrate::test_cook_data_platform", "dice_elipy_scripts/tests/test_integrate_compile_upgrade_cook.py::TestIntegrate::test_cook_pipeline_args", "dice_elipy_scripts/tests/test_integrate_compile_upgrade_cook.py::TestIntegrate::test_no_submit", "dice_elipy_scripts/tests/test_integrate_compile_upgrade_cook.py::TestIntegrate::test_submit", "dice_elipy_scripts/tests/test_integrate_compile_upgrade_cook.py::TestIntegrate::test_submit_with_message", "dice_elipy_scripts/tests/test_integrate_compile_upgrade_cook.py::TestIntegrate::test_unresolved", "dice_elipy_scripts/tests/test_integrate_compile_upgrade_cook.py::TestIntegrate::test_upgrade_exception", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_basic_run", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_abortforfiles", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_abortforfiles_failure", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_cleanup_rules", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_cleanup_rules_no_cleanup_workspace", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_fdu", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_fdu_failure", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_fdu_failure_run_clean", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_fdu_failure_unresolved", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_install_dotnet", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_integrate", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_loginfo", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_resolve", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_revert", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_revert_dont_wipe", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_revert_unsupported_flag", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_branch_guardian_type_not_defined", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_compile_code_clean", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_compile_code_default", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_compile_code_framework_args", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_compile_code_framework_args_branch_guardian", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_compile_code_framework_args_branch_guardian_use_preview", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_cook_data", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_cook_data_clean", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_cook_data_other_platform", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_cook_data_set_datadir", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_copy_mapping", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_copy_mapping_reverse", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_dbxmerge_basic", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_dbxmerge_fdu_unresolved", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_dbxmerge_no_dbx_files_found", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_integrate_mapping", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_integrate_mapping_reverse", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_integration_unresolved", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_local_upgrade", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_local_upgrade_exception", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_local_upgrade_exception_run_clean", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_local_upgrade_script_path", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_no_mapping", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_no_submit", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_run_upgrade_exception", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_run_upgrade_exception_run_clean", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_run_upgrade_multiple_licensees", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_run_upgrade_script_path", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_run_upgrade_single_licensee", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_skip_cook", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_skip_upgrade", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_submit", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_submit_branch_guardian", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_submit_changes_range_default", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_submit_changes_range_empty_string_last_changelist", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_submit_revert_branchid_file", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_submit_shelve_cl", "dice_elipy_scripts/tests/test_integrate_upgrade_one_stream.py::TestIntegrateUpgradeOneStream::test_sync_data_changelist", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_compile_code_buildsln", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_compile_code_buildsln_framework_args", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_compile_code_default", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_compile_code_p4_settings", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_compile_code_snowcache_default", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_cook_data_clean_avalanche_cook", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_cook_data_copy_code_no_code_branch", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_cook_data_default", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_cook_data_import_avalanche", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_cook_data_import_avalanche_missing_args", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_cook_data_pipeline_args", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_cook_data_use_local_code", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_no_submit_shelve_cl", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_no_submit_shelve_cl_no_pending_changelist", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_no_submit_skip_shelve_cl", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_submit_integration", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_submit_integration_data_upgrade", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_submit_integration_data_upgrade_revert_branchid_file", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_submit_integration_data_upgrade_submit_folder", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_submit_integration_no_submit", "dice_elipy_scripts/tests/test_integration_utils.py::TestIntegrationUtils::test_submit_shelve_cl", "dice_elipy_scripts/tests/test_licensee_helper.py::TestLicenseeHelper::test_check_enabled_licensee_2022", "dice_elipy_scripts/tests/test_licensee_helper.py::TestLicenseeHelper::test_check_enabled_licensee_double_licensees", "dice_elipy_scripts/tests/test_licensee_helper.py::TestLicenseeHelper::test_check_enabled_licensee_no_licensee", "dice_elipy_scripts/tests/test_licensee_helper.py::TestLicenseeHelper::test_check_enabled_licensee_pre_2022", "dice_elipy_scripts/tests/test_licensee_helper.py::TestLicenseeHelper::test_check_enabled_licensee_wrong_licensee", "dice_elipy_scripts/tests/test_licensee_helper.py::TestLicenseeHelper::test_set_licensee_2020_dict_internal", "dice_elipy_scripts/tests/test_licensee_helper.py::TestLicenseeHelper::test_set_licensee_2020_with_licensee_with_file", "dice_elipy_scripts/tests/test_licensee_helper.py::TestLicenseeHelper::test_set_licensee_2020_with_licensee_without_file", "dice_elipy_scripts/tests/test_licensee_helper.py::TestLicenseeHelper::test_set_licensee_2022_dict_api", "dice_elipy_scripts/tests/test_licensee_helper.py::TestLicenseeHelper::test_set_licensee_correct_licensee_enabled_does_not_set_licensee", "dice_elipy_scripts/tests/test_licensee_helper.py::TestLicenseeHelper::test_set_licensee_no_licensee", "dice_elipy_scripts/tests/test_licensee_helper.py::TestLicenseeHelper::test_set_licensee_tuple_argument", "dice_elipy_scripts/tests/test_licensee_helper.py::TestLicenseeHelper::test_set_licensee_with_licensee_pre_2020", "dice_elipy_scripts/tests/test_move_location_bundles.py::TestMoveLocationBundles::test_dest_path_already_exists", "dice_elipy_scripts/tests/test_move_location_bundles.py::TestMoveLocationBundles::test_move_location_bundles_standard", "dice_elipy_scripts/tests/test_move_location_bundles.py::TestMoveLocationBundles::test_move_location_combine_bundles", "dice_elipy_scripts/tests/test_move_location_drone.py::TestMoveLocationDrone::test_move_location_drone_previous_copy_complete", "dice_elipy_scripts/tests/test_move_location_drone.py::TestMoveLocationDrone::test_move_location_drone_previous_copy_incomplete", "dice_elipy_scripts/tests/test_move_location_drone.py::TestMoveLocationDrone::test_move_location_drone_robocopy_exception", "dice_elipy_scripts/tests/test_move_location_drone.py::TestMoveLocationDrone::test_move_location_drone_success", "dice_elipy_scripts/tests/test_nant.py::TestNant::test_nant_base", "dice_elipy_scripts/tests/test_nant.py::TestNant::test_nant_enable_deprecation", "dice_elipy_scripts/tests/test_nant.py::TestNant::test_nant_enable_expirederror", "dice_elipy_scripts/tests/test_nant.py::TestNant::test_nant_framework_args", "dice_elipy_scripts/tests/test_nant.py::TestNant::test_nant_fwderror", "dice_elipy_scripts/tests/test_nant.py::TestNant::test_nant_fwdwarn", "dice_elipy_scripts/tests/test_nant.py::TestNant::test_nant_ignore_deprecation", "dice_elipy_scripts/tests/test_nant.py::TestNant::test_nant_licensee_agnostic", "dice_elipy_scripts/tests/test_nant.py::TestNant::test_nant_outsourcer", "dice_elipy_scripts/tests/test_nant.py::TestNant::test_nant_outsourcer_non_proxy", "dice_elipy_scripts/tests/test_nant.py::TestNant::test_nant_propertiesfile", "dice_elipy_scripts/tests/test_nant.py::TestNant::test_nant_validate_package_access", "dice_elipy_scripts/tests/test_nant.py::TestNant::test_nant_vsver", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildOffsiteBuild::test_destination_exists", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildOffsiteBuild::test_destination_exists_provided", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildsCli::test_add_fingerprint", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildsCli::test_basic_args", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildsCli::test_basic_drone_build", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildsCli::test_basic_drone_zip", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildsCli::test_create_zip_called", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildsCli::test_create_zip_failure", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildsCli::test_get_basic_drone_source", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildsCli::test_get_full_drone_source", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildsCli::test_offsitebuild_internal", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildsCli::test_offsitebuild_internal_outsourcers", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildsCli::test_outsourcers", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildsCli::test_outsourcers_multiple", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildsCli::test_update_eacopy_file_list", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildsWithMetadata::test_is_qa_qualify_not_verified", "dice_elipy_scripts/tests/test_offsitebuild.py::TestOffsiteBuildsWithMetadata::test_is_qa_qualify_verified", "dice_elipy_scripts/tests/test_outsource_package.py::TestOutsourcePackage::test_basic_run", "dice_elipy_scripts/tests/test_outsource_package.py::TestOutsourcePackage::test_clean_delete_folder", "dice_elipy_scripts/tests/test_outsource_package.py::TestOutsourcePackage::test_data_directory", "dice_elipy_scripts/tests/test_outsource_package.py::TestOutsourcePackage::test_delete_folder_without_clean", "dice_elipy_scripts/tests/test_outsource_package.py::TestOutsourcePackage::test_dry_run", "dice_elipy_scripts/tests/test_outsource_package.py::TestOutsourcePackage::test_import_module", "dice_elipy_scripts/tests/test_outsource_package.py::TestOutsourcePackage::test_p4_reconcile", "dice_elipy_scripts/tests/test_outsource_package.py::TestOutsourcePackage::test_p4_submit", "dice_elipy_scripts/tests/test_outsource_package.py::TestOutsourcePackage::test_run_gensln", "dice_elipy_scripts/tests/test_outsource_package.py::TestOutsourcePackage::test_run_package_script", "dice_elipy_scripts/tests/test_p4_copy.py::TestP4CopyDataUpgradeCli::test_basic_args", "dice_elipy_scripts/tests/test_p4_copy.py::TestP4CopyDataUpgradeCli::test_exclude_path", "dice_elipy_scripts/tests/test_p4_copy.py::TestP4CopyDataUpgradeCli::test_no_submit", "dice_elipy_scripts/tests/test_p4_copy.py::TestP4CopyDataUpgradeCli::test_submit_default", "dice_elipy_scripts/tests/test_p4_copy.py::TestP4CopyDataUpgradeCli::test_submit_extra_message", "dice_elipy_scripts/tests/test_p4_copy.py::TestP4CopyDataUpgradeCli::test_submit_source_target_branches", "dice_elipy_scripts/tests/test_p4_copy_data_upgrade.py::TestP4CopyDataUpgradeCli::test_basic_args", "dice_elipy_scripts/tests/test_p4_copy_data_upgrade.py::TestP4CopyDataUpgradeCli::test_compile_code", "dice_elipy_scripts/tests/test_p4_copy_data_upgrade.py::TestP4CopyDataUpgradeCli::test_compile_code_clean", "dice_elipy_scripts/tests/test_p4_copy_data_upgrade.py::TestP4CopyDataUpgradeCli::test_compile_code_licensee", "dice_elipy_scripts/tests/test_p4_copy_data_upgrade.py::TestP4CopyDataUpgradeCli::test_copy_mapping", "dice_elipy_scripts/tests/test_p4_copy_data_upgrade.py::TestP4CopyDataUpgradeCli::test_exclude_path", "dice_elipy_scripts/tests/test_p4_copy_data_upgrade.py::TestP4CopyDataUpgradeCli::test_exclude_path_multiple", "dice_elipy_scripts/tests/test_p4_copy_data_upgrade.py::TestP4CopyDataUpgradeCli::test_no_exclude", "dice_elipy_scripts/tests/test_p4_copy_data_upgrade.py::TestP4CopyDataUpgradeCli::test_no_submit", "dice_elipy_scripts/tests/test_p4_copy_data_upgrade.py::TestP4CopyDataUpgradeCli::test_set_datadir", "dice_elipy_scripts/tests/test_p4_copy_data_upgrade.py::TestP4CopyDataUpgradeCli::test_submit", "dice_elipy_scripts/tests/test_p4_copy_data_upgrade.py::TestP4CopyDataUpgradeCli::test_submit_extra_message", "dice_elipy_scripts/tests/test_p4_copy_data_upgrade.py::TestP4CopyDataUpgradeCli::test_upgrade_exception", "dice_elipy_scripts/tests/test_p4_counter.py::P4CounterTests::test_basic_args", "dice_elipy_scripts/tests/test_p4_counter.py::P4CounterTests::test_set_extra_counter_failure", "dice_elipy_scripts/tests/test_p4_counter.py::P4CounterTests::test_set_extra_counter_force_set_lower_value", "dice_elipy_scripts/tests/test_p4_counter.py::P4CounterTests::test_set_extra_counter_on_another_port", "dice_elipy_scripts/tests/test_p4_counter.py::P4CounterTests::test_set_extra_counter_on_same_port", "dice_elipy_scripts/tests/test_p4_counter.py::P4CounterTests::test_set_extra_counter_skip_lower_value", "dice_elipy_scripts/tests/test_p4_counter.py::P4CounterTests::test_setcounter", "dice_elipy_scripts/tests/test_p4_counter.py::P4CounterTests::test_setcounter_failure", "dice_elipy_scripts/tests/test_p4_counter.py::P4CounterTests::test_setcounter_force_set_lower_value", "dice_elipy_scripts/tests/test_p4_counter.py::P4CounterTests::test_setcounter_skip_lower_value", "dice_elipy_scripts/tests/test_p4_counter.py::TestCounterLocalFunctions::test_get_counter_as_int_input_as_bytes", "dice_elipy_scripts/tests/test_p4_counter.py::TestCounterLocalFunctions::test_get_counter_as_int_input_as_int", "dice_elipy_scripts/tests/test_p4_counter.py::TestCounterLocalFunctions::test_get_counter_as_int_input_as_string", "dice_elipy_scripts/tests/test_p4_counter.py::TestCounterLocalFunctions::test_get_counter_as_int_type_error", "dice_elipy_scripts/tests/test_p4_dvcs.py::P4DvcsTests::test_p4_dvcs_fetch", "dice_elipy_scripts/tests/test_p4_dvcs.py::P4DvcsTests::test_p4_dvcs_push", "dice_elipy_scripts/tests/test_p4_dvcs.py::P4DvcsTests::test_p4_dvcs_push_missing_remote", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_basic_run", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_clean_default", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_clean_skip_for_staging_stream", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_clean_use_for_staging_stream", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_clean_virtual_branch_override", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_clear_cache", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_clear_cache_no_baseline_import_avalanche", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_clear_cache_no_baseline_set", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_clear_cache_skip_import_baseline_state", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_ddelta", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_ddelta_combine", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_ddelta_combine_use_head_as_base", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_ddelta_use_head_as_base", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_deploy_state", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_deploy_state_combine", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_dest_exists_exception", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_export_combine_bundles", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_export_enable_compression", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_export_no_compression", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_export_recompression_cache", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_fetch_baseline_bundles", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_fetch_baseline_bundles_first_patch", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_fetch_baseline_combine_bundles", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_fetch_baseline_combine_bundles_first_patch", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_fetch_baseline_state", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_fetch_baseline_state_combine", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_fetch_baseline_state_combine_standalone_baseline", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_fetch_baseline_state_first_patch", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_pipeline_args_run", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_run_expression_debug_data", "dice_elipy_scripts/tests/test_patch_databuild.py::TestPatchDatabuild::test_virtual_branch_override", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_basepackage_in_frosty_args_when_fetch_xb_basepackage_true_in_config_and_xbox", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_basepackage_not_in_frosty_args_when_fetch_xb_basepackage_false_in_config_and_xbox", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_basepackage_not_in_frosty_args_when_fetch_xb_basepackage_not_in_config_and_xbox", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_basepackage_with_disc_options_fetched_when_fetch_xb_basepackage_true_in_config", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_basepackage_with_disc_options_not_fetched_when_fetch_xb_basepackage_false_in_config", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_basepackage_with_disc_options_not_fetched_when_fetch_xb_basepackage_not_in_config", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_combine_bundles_run_with_combine_settings_file", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_copy_submissionvalidator_xb1", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_copy_submissionvalidator_xbsx", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_deploy_avalanche_combine_output", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_deploy_frosty_build", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_deploy_frosty_build_combine", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_fetch_baseline_ps_package", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_fetch_baseline_ps_package_combine", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_fetch_baseline_ps_package_first_patch", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_fetch_baseline_win64_chunkmanifest_combine", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_fetch_baseline_win64_chunkmanifest_combine_first_patch", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_fetch_baseline_win64_chunkmanifest_non_win64", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_fetch_baseline_win64_chunkmanifest_win64", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_fetch_baseline_win64_chunkmanifest_win64_first_patch", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_fetch_code", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_fetch_code_use_combine", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_fetch_code_win64game", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_fetch_code_win64game_win64trial", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_fetch_contversion", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_fetch_contversion_combine", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_first_patch_xbsx", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_first_patch_xbsx_combine", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_frostyisotool_exception", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_not_copy_submissionvalidator_new_fb", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_not_copy_submissionvalidator_ps4", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_not_first_patch_xbsx", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_not_first_patch_xbsx_combine", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_ps4", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_ps5", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_steam_build", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_use_combine_bundles_basic", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_use_combine_bundles_run_combine", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_use_combine_bundles_run_combine_xbsx", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_use_combine_ddelta", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_use_combine_ddelta_use_head_as_base", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_use_combine_fetch_baseline", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_use_combine_fetch_baseline_first_patch", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_use_combine_fetch_baseline_first_patch_standalone_disc_baseline", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_use_combine_fetch_baseline_standalone_patch_baseline", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_virtual_branch_override", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_win64", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_win64_oreans", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_xb1_battlefieldgame", "dice_elipy_scripts/tests/test_patch_frosty.py::TestPatchFrosty::test_xbsx_battlefieldgame", "dice_elipy_scripts/tests/test_pipeline_determinism_test.py::TestPipelineDeterminismTest::test_pipeline_determinism_test_with_args", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_clean_delete_folder", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_data_directory", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_delete_folder_without_clean", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_nintendo_switch", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_prebuild_dry_run", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_prebuild_fbenv_call", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_prebuild_fbenv_call_multiple_pkg_platforms", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_prebuild_gensln", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_prebuild_reconcile", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_prebuild_submit", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_skip_platform", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_submission_run", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_validation_buildsln", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_validation_exception", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_validation_install", "dice_elipy_scripts/tests/test_prebuild.py::TestPrebuild::test_validation_run", "dice_elipy_scripts/tests/test_preflight_utils.py::TestRaiseIfClNotExists::test_describe_timeout", "dice_elipy_scripts/tests/test_preflight_utils.py::TestRaiseIfClNotExists::test_found_cl", "dice_elipy_scripts/tests/test_preflight_utils.py::TestRaiseIfClNotExists::test_not_found_cl", "dice_elipy_scripts/tests/test_preflight_utils.py::TestRaiseIfWrongStream::test_right_stream", "dice_elipy_scripts/tests/test_preflight_utils.py::TestRaiseIfWrongStream::test_right_stream_case_sensitive", "dice_elipy_scripts/tests/test_preflight_utils.py::TestRaiseIfWrongStream::test_wrong_stream", "dice_elipy_scripts/tests/test_preflight_utils.py::TestTruncateLogList::test_truncate_log_list_with_empty_list", "dice_elipy_scripts/tests/test_preflight_utils.py::TestTruncateLogList::test_truncate_log_list_with_empty_list_and_message_prefix", "dice_elipy_scripts/tests/test_preflight_utils.py::TestTruncateLogList::test_truncate_log_list_with_long_list", "dice_elipy_scripts/tests/test_preflight_utils.py::TestTruncateLogList::test_truncate_log_list_with_long_list_and_message_prefix", "dice_elipy_scripts/tests/test_preflight_utils.py::TestTruncateLogList::test_truncate_log_list_with_message_prefix", "dice_elipy_scripts/tests/test_preflight_utils.py::TestTruncateLogList::test_truncate_log_list_with_short_list", "dice_elipy_scripts/tests/test_prepare_vm.py::test_configure_avalanche", "dice_elipy_scripts/tests/test_prepare_vm.py::test_confirm_connectivity_404_error", "dice_elipy_scripts/tests/test_prepare_vm.py::test_confirm_connectivity_connection_error", "dice_elipy_scripts/tests/test_prepare_vm.py::test_confirm_fileshare_path_not_exists", "dice_elipy_scripts/tests/test_prepare_vm.py::test_confirm_perforce_process_error", "dice_elipy_scripts/tests/test_prepare_vm.py::test_prepare_vm_azure_hosted", "dice_elipy_scripts/tests/test_prepare_vm.py::test_prepare_vm_self_hosted", "dice_elipy_scripts/tests/test_process_shift_subscription_downloads.py::TestFindFileWithPrefix::test_find_file_with_prefix_first", "dice_elipy_scripts/tests/test_process_shift_subscription_downloads.py::TestFindFileWithPrefix::test_find_file_with_prefix_not_found", "dice_elipy_scripts/tests/test_process_shift_subscription_downloads.py::TestFindFileWithPrefix::test_find_file_with_prefix_second", "dice_elipy_scripts/tests/test_process_shift_subscription_downloads.py::TestProcessShiftSubscriptionDownloadsCli::test_basic_args", "dice_elipy_scripts/tests/test_process_shift_subscription_downloads.py::TestProcessShiftSubscriptionDownloadsCli::test_failure_capturing", "dice_elipy_scripts/tests/test_process_shift_subscription_downloads.py::TestProcessShiftSubscriptionDownloadsCli::test_failure_non_implemented_build_type", "dice_elipy_scripts/tests/test_process_shift_subscription_downloads.py::TestRegisterBuildInBilbo::test_register_build_in_bilbo", "dice_elipy_scripts/tests/test_process_shift_subscription_downloads.py::TestRegisterBuildInBilbo::test_register_build_in_bilbo_raises_exception_when_no_build_json_file_found", "dice_elipy_scripts/tests/test_requirements.py::test_elipy_scripts_requirements_txt_aligned_with_elipy_scripts_setup_py", "dice_elipy_scripts/tests/test_sentry_utils.py::TestSentryUtilsAddFrostbiteSentryTags::test_add_frostbite_sentry_tags", "dice_elipy_scripts/tests/test_sentry_utils.py::TestSentryUtilsAddFrostbiteSentryTags::test_add_frostbite_sentry_tags_exception", "dice_elipy_scripts/tests/test_sentry_utils.py::TestSentryUtilsSetTags::test_set_tag", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_avalanche_api_endpoint_built_correctly_from_settings_avalanche_host[avalanche_status0]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_avalanche_upstream_requirements_failure_logs_warning[ConnectTimeout-1]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_avalanche_upstream_requirements_failure_logs_warning[ConnectionError-1]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_avalanche_upstream_requirements_failure_logs_warning[HTTPError-1]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_avalanche_upstream_requirements_failure_logs_warning[ReadTimeout-1]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_avalanche_upstream_requirements_failure_logs_warning[get_status_side_effect3-1]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_avalanche_upstream_requirements_failure_logs_warning[get_status_side_effect4-1]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_avalanche_upstream_requirements_failure_logs_warning[get_status_side_effect5-0]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_avalanche_upstream_server_healthy_results_appropriate[ConnectionError-False]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_avalanche_upstream_server_healthy_results_appropriate[HTTPError-False]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_avalanche_upstream_server_healthy_results_appropriate[ReadTimeout-False]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_avalanche_upstream_server_healthy_results_appropriate[get_status_side_effect0-True]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_avalanche_upstream_server_healthy_results_appropriate[get_status_side_effect1-True]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_avalanche_upstream_server_healthy_results_appropriate[get_status_side_effect3-False]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_avalanche_upstream_server_healthy_results_appropriate[get_status_side_effect4-False]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_clean_required_logic[False-upload-False-False]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_clean_required_logic[False-upload-True-True]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_clean_required_logic[True--False-True]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_clean_required_logic[True--True-True]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_clean_required_logic[True-forceupload-False-True]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_clean_required_logic[True-forceupload-True-True]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_clean_required_logic[True-upload-False-False]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_clean_required_logic[True-upload-True-True]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode[False--uploadanddownload0]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode[False--uploadanddownload1]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode[False-forceupload-forceupload]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode[False-upload-upload0]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode[False-upload-upload1]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode[False-upload-upload2]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode[True--forceupload0]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode[True--forceupload1]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode[True-upload-upload0]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode[True-upload-upload1]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode_args[False-forceupload-False-forceupload]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode_args[False-upload-False-upload]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode_args[False-upload-True-upload0]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode_args[False-upload-True-upload1]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode_args[True--False-expected_result1]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode_args[True--True-forceupload]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode_args[True-upload-False-upload]", "dice_elipy_scripts/tests/test_snowcache_utils.py::TestSnowcacheUtils::test_get_snowcache_mode_args[True-upload-True-upload]", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_export_head_bundles", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_export_head_bundles_extra_args", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_export_head_bundles_skip_ordering_algorithm", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_export_head_bundles_skip_platform", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_export_head_bundles_specify_ordering_algorithm", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_import_avalanche_data_state_changelists_missing", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_import_avalanche_data_state_db_exists", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_import_avalanche_data_state_exception", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_import_avalanche_data_state_no_data_branch_specified", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_import_avalanche_data_state_not_running", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_import_avalanche_data_state_remote_clone", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_import_avalanche_data_state_remote_host_not_existing", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_import_local_code_state", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_import_local_code_state_dir_not_found", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_import_local_code_state_exception", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_import_local_code_state_nomaster", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_import_local_code_state_old_fbenv", "dice_elipy_scripts/tests/test_state_utils.py::TestStateUtils::test_import_local_code_state_tool", "dice_elipy_scripts/tests/test_submit_to_shift.py::TestShifterFactory::test_get_shifter_instance_offsite_basic_drone_shifter[frosty_shifter]", "dice_elipy_scripts/tests/test_submit_to_shift.py::TestShifterFactory::test_get_shifter_instance_offsite_basic_drone_shifter[offsite_basic_drone_shifter]", "dice_elipy_scripts/tests/test_submit_to_shift.py::TestShifterFactory::test_get_shifter_instance_offsite_basic_drone_shifter[offsite_drone_shifter]", "dice_elipy_scripts/tests/test_submit_to_shift.py::TestSubmitToShift::test_shifter_process_shift_upload_called[frosty_shifter]", "dice_elipy_scripts/tests/test_submit_to_shift.py::TestValidateDataChangelist::test_data_changelist_not_set_in_non_unified_project", "dice_elipy_scripts/tests/test_submit_to_shift.py::TestValidateDataChangelist::test_data_changelist_not_set_in_unified_project", "dice_elipy_scripts/tests/test_submit_to_shift.py::TestValidateDataChangelist::test_data_changelist_set_in_non_unified_project", "dice_elipy_scripts/tests/test_submit_to_shift.py::TestValidateDataChangelist::test_data_changelist_set_in_unified_project", "dice_elipy_scripts/tests/test_submit_to_spin.py::TestSubmitToSpin::test_cli", "dice_elipy_scripts/tests/test_submit_to_spin.py::TestSubmitToSpin::test_collect_files_to_submit_failure_raises_exception", "dice_elipy_scripts/tests/test_submit_to_spin.py::TestSubmitToSpin::test_collect_files_to_submit_success", "dice_elipy_scripts/tests/test_submit_to_spin.py::TestSubmitToSpin::test_collect_files_to_submit_success_zipped", "dice_elipy_scripts/tests/test_submit_to_spin.py::TestSubmitToSpin::test_prepare_aws_for_spin", "dice_elipy_scripts/tests/test_submit_to_spin.py::TestSubmitToSpin::test_prepare_aws_for_spin_credentials_missing", "dice_elipy_scripts/tests/test_submit_to_spin.py::TestSubmitToSpin::test_prepare_aws_for_spin_credentials_none", "dice_elipy_scripts/tests/test_submit_to_spin.py::TestSubmitToSpin::test_upload_to_spin", "dice_elipy_scripts/tests/test_symbol_store_upload.py::TestSymbolStoreUploadCli::test_basic_args", "dice_elipy_scripts/tests/test_symbol_store_upload.py::TestSymbolStoreUploadCli::test_deploy_code", "dice_elipy_scripts/tests/test_symbol_store_upload.py::TestSymbolStoreUploadCli::test_deploy_code_skip_bilbo", "dice_elipy_scripts/tests/test_symbol_store_upload.py::TestSymbolStoreUploadCli::test_install_required_sdks", "dice_elipy_scripts/tests/test_symbol_store_upload.py::TestSymbolStoreUploadCli::test_install_required_sdks_credentials", "dice_elipy_scripts/tests/test_symbol_store_upload.py::TestSymbolStoreUploadCli::test_set_licensee", "dice_elipy_scripts/tests/test_symbol_store_upload.py::TestSymbolStoreUploadCli::test_source_index", "dice_elipy_scripts/tests/test_symbol_store_upload.py::TestSymbolStoreUploadCli::test_upload_symbols_to_sym_store", "dice_elipy_scripts/tests/test_symbol_store_upload.py::TestSymbolStoreUploadCli::test_upload_symbols_to_sym_store_dont_compress", "dice_elipy_scripts/tests/test_symbol_store_upload.py::TestSymbolStoreUploadCli::test_upload_symbols_to_sym_store_with_index_custom_cmd", "dice_elipy_scripts/tests/test_symbol_store_upload.py::TestSymbolStoreUploadCli::test_upload_symbols_to_sym_store_with_tool_path", "dice_elipy_scripts/tests/test_symbol_store_upload.py::TestSymbolStoreUploadCli::test_upload_symbols_to_sym_store_with_verify", "dice_elipy_scripts/tests/test_symbol_store_upload.py::TestSymbolStoreUploadCli::test_verify_symbol", "dice_elipy_scripts/tests/test_test_nant.py::TestTestNant::test_gensln", "dice_elipy_scripts/tests/test_test_nant.py::TestTestNant::test_linux", "dice_elipy_scripts/tests/test_test_nant.py::TestTestNant::test_retest_failures", "dice_elipy_scripts/tests/test_test_nant.py::TestTestNant::test_show_output", "dice_elipy_scripts/tests/test_test_nant.py::TestTestNant::test_testname", "dice_elipy_scripts/tests/test_test_runner.py::TestTestRunner::test_gtest_arguments_2", "dice_elipy_scripts/tests/test_test_runner.py::TestTestRunner::test_multiple_custom_config", "dice_elipy_scripts/tests/test_test_runner.py::TestTestRunner::test_one_custom_config", "dice_elipy_scripts/tests/test_testutils.py::TestGotButExpected::test_got_but_expected", "dice_elipy_scripts/tests/test_trigger_docker_image_gitlab_pipeline.py::TestTriggerGitLabPipeline::test_trigger_pipeline_failure", "dice_elipy_scripts/tests/test_trigger_docker_image_gitlab_pipeline.py::TestTriggerGitLabPipeline::test_trigger_pipeline_success", "dice_elipy_scripts/tests/test_trigger_docker_image_gitlab_pipeline.py::TestTriggerGitLabPipeline::test_trigger_pipeline_timeout", "dice_elipy_scripts/tests/test_unittests.py::TestUnittests::test_unittests_raises_exception_on_error", "dice_elipy_scripts/tests/test_unittests.py::TestUnittests::test_unittests_runs_script", "dice_elipy_scripts/tests/test_upload_to_steam.py::TestGetPackageType::test_get_package_type[False-None-steam]", "dice_elipy_scripts/tests/test_upload_to_steam.py::TestGetPackageType::test_get_package_type[False-feature_branch-steam_combine]", "dice_elipy_scripts/tests/test_upload_to_steam.py::TestGetPackageType::test_get_package_type[True-None-steam_patch]", "dice_elipy_scripts/tests/test_upload_to_steam.py::TestGetPackageType::test_get_package_type[True-feature_branch-steam_patch_combine]", "dice_elipy_scripts/tests/test_upload_to_steam.py::TestUploadPatchBuildOptions::test_upload_patch_build_options[cli_args0-True-None]", "dice_elipy_scripts/tests/test_upload_to_steam.py::TestUploadPatchBuildOptions::test_upload_patch_build_options[cli_args1-False-None]", "dice_elipy_scripts/tests/test_upload_to_steam.py::TestUploadPatchBuildOptions::test_upload_patch_build_options[cli_args2-True-feature]", "dice_elipy_scripts/tests/test_upload_to_steam.py::TestUploadPatchBuildOptions::test_upload_patch_build_options[cli_args3-False-feature]", "dice_elipy_scripts/tests/test_upload_to_steam.py::TestUploadToSteam::test_cli_file_not_found", "dice_elipy_scripts/tests/test_upload_to_steam.py::TestUploadToSteam::test_upload_to_steam_invalid_secrets", "dice_elipy_scripts/tests/test_upload_to_steam.py::TestUploadToSteam::test_upload_to_steam_no_secrets", "dice_elipy_scripts/tests/test_upload_to_steam.py::TestUploadToSteam::test_upload_to_steam_success", "dice_elipy_scripts/tests/test_upload_to_steam.py::TestUploadToSteam::test_upload_to_steam_system_error", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_get_config_files_contents", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_get_config_path_pvv", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_get_destination_base_pvv", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_post_vaulting_verification", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_post_vaulting_verification_alt_location", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_post_vaulting_verification_error", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_post_vaulting_verification_run", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_post_vaulting_verification_success_fails_when_vault_layout_invalid", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_post_vaulting_verification_success_with_vault_layout_passed", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_post_vaulting_verification_success_without_vault_layout_passed", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_vault_build", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_vault_symbols", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_vault_symbols_with_combined_builds", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_vault_symbols_with_empty_string_build_location", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_vault_symbols_with_none_build_location", "dice_elipy_scripts/tests/test_vault.py::TestVault::test_vault_symbols_with_value_build_location", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_avalanche_nuke", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_cli_basic_args", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_cli_basic_args_branch_override_empty_string", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_cli_basic_args_branch_override_set", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_cli_basic_args_no_branch_override", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_cli_webexport_args", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_data_clean", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_dont_import_avalanche", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_dont_save_to_filer", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_fetch_pipeline_bin", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_filer_save", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_filer_save_no_directory", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_filer_save_no_patchversion", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_get_webx_module", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_get_webx_module_exception", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_get_webx_module_not_in_tnt", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_imp_prev_avalanche", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_import_avalanche", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_run_webexport", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_save_to_filer", "dice_elipy_scripts/tests/test_webexport.py::TestWebexport::test_save_to_filer_with_content_layer"]